<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pattern Helper 简单测试</title>
    <link rel="stylesheet" href="/static/css/pattern_helper.css">
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        .test-section { 
            margin: 20px 0; 
            padding: 15px; 
            border: 1px solid #ddd; 
            border-radius: 5px;
        }
        .console-output { 
            background: #f8f8f8; 
            padding: 10px; 
            margin: 10px 0; 
            font-family: monospace; 
            border-radius: 4px;
            max-height: 200px;
            overflow-y: auto;
        }
        button { 
            padding: 10px 20px; 
            margin: 5px; 
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🦄 Pattern Helper 简单测试</h1>
        
        <div class="test-section">
            <h2>1. 初始化检查</h2>
            <button type="button" onclick="checkInitialization()">检查初始化状态</button>
            <div id="init-result" class="console-output"></div>
        </div>
        
        <div class="test-section">
            <h2>2. 手动触发事件</h2>
            <button type="button" onclick="triggerWordChanged()">触发 rainy 单词事件</button>
            <div id="event-result" class="console-output"></div>
        </div>
        
        <div class="test-section">
            <h2>3. 直接API测试</h2>
            <button type="button" onclick="testAPI()">测试 API 调用</button>
            <div id="api-result" class="console-output"></div>
        </div>
        
        <div class="test-section">
            <h2>4. 手动显示图标</h2>
            <button type="button" onclick="forceShowIcon()">强制显示浮动图标</button>
            <div id="icon-result" class="console-output"></div>
        </div>
        
        <div class="test-section">
            <h2>5. 控制台日志</h2>
            <p>请打开浏览器开发者工具的控制台查看详细日志</p>
            <button type="button" onclick="clearConsole()">清空控制台</button>
        </div>
    </div>

    <script>
        // 等待页面加载完成
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🦄 测试页面加载完成');
            
            // 延迟检查Pattern Helper
            setTimeout(() => {
                checkInitialization();
            }, 1000);
        });

        function checkInitialization() {
            const resultDiv = document.getElementById('init-result');
            let status = [];
            
            // 检查PatternHelper类是否存在
            if (typeof PatternHelper !== 'undefined') {
                status.push('✅ PatternHelper 类已定义');
            } else {
                status.push('❌ PatternHelper 类未定义');
            }
            
            // 检查全局实例
            if (typeof window.patternHelper !== 'undefined') {
                status.push('✅ PatternHelper 实例已创建');
                status.push(`   - 当前单词ID: ${window.patternHelper.currentWordId}`);
                status.push(`   - 是否可见: ${window.patternHelper.isVisible}`);
                status.push(`   - 面板是否打开: ${window.patternHelper.isPanelOpen}`);
            } else {
                status.push('❌ PatternHelper 实例未创建');
            }
            
            // 检查DOM元素
            const floatingIcon = document.getElementById('pattern-floating-icon');
            if (floatingIcon) {
                status.push('✅ 浮动图标元素存在');
                status.push(`   - 类名: ${floatingIcon.className}`);
                status.push(`   - 样式: display=${getComputedStyle(floatingIcon).display}, opacity=${getComputedStyle(floatingIcon).opacity}`);
            } else {
                status.push('❌ 浮动图标元素不存在');
            }
            
            const slidePanel = document.getElementById('pattern-slide-panel');
            if (slidePanel) {
                status.push('✅ 滑出面板元素存在');
            } else {
                status.push('❌ 滑出面板元素不存在');
            }
            
            resultDiv.innerHTML = status.join('<br>');
        }

        function triggerWordChanged() {
            const resultDiv = document.getElementById('event-result');
            
            const wordData = {
                id: 64,
                english_word: 'rainy',
                chinese_meaning: '下雨',
                item_type: 'word',
                star_level: 4
            };
            
            console.log('🦄 手动触发 wordChanged 事件:', wordData);
            
            document.dispatchEvent(new CustomEvent('wordChanged', {
                detail: wordData
            }));
            
            resultDiv.innerHTML = `
                ✅ wordChanged 事件已触发<br>
                单词: ${wordData.english_word} (ID: ${wordData.id})<br>
                请查看控制台查看 Pattern Helper 的响应
            `;
        }

        async function testAPI() {
            const resultDiv = document.getElementById('api-result');
            resultDiv.innerHTML = '正在测试 API...';
            
            try {
                const response = await fetch('/api/word_pattern_suggestions/64', {
                    method: 'GET',
                    credentials: 'same-origin'
                });
                
                console.log('🦄 API 响应状态:', response.status);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }
                
                const result = await response.json();
                console.log('🦄 API 响应数据:', result);
                
                let output = [];
                output.push(`✅ API 调用成功 (状态码: ${response.status})`);
                output.push(`成功标志: ${result.success}`);
                output.push(`有推荐: ${result.data?.has_recommendations}`);
                output.push(`推荐数量: ${result.data?.recommendations?.length || 0}`);
                
                if (result.data?.recommendations?.length > 0) {
                    const rec = result.data.recommendations[0];
                    output.push(`第一个推荐:`);
                    output.push(`  - 类型: ${rec.pattern_info?.pattern_type}`);
                    output.push(`  - 名称: ${rec.pattern_info?.pattern_name}`);
                    output.push(`  - 语言学专家: ${rec.pattern_info?.is_linguistic_expert}`);
                    output.push(`  - 相似词数量: ${rec.similar_words?.length || 0}`);
                }
                
                resultDiv.innerHTML = output.join('<br>');
                
            } catch (error) {
                console.error('🦄 API 调用失败:', error);
                resultDiv.innerHTML = `❌ API 调用失败: ${error.message}`;
            }
        }

        function forceShowIcon() {
            const resultDiv = document.getElementById('icon-result');
            
            if (window.patternHelper) {
                console.log('🦄 强制显示浮动图标');
                window.patternHelper.showFloatingIcon();
                resultDiv.innerHTML = '✅ 已调用 showFloatingIcon()，请查看页面右下角';
            } else {
                resultDiv.innerHTML = '❌ PatternHelper 实例不存在';
            }
        }

        function clearConsole() {
            console.clear();
            console.log('🦄 控制台已清空');
        }
    </script>
    
    <!-- 包含 Pattern Helper -->
    <script src="/static/js/pattern_helper.js"></script>
</body>
</html>
