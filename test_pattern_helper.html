<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pattern Helper 测试</title>
    <link rel="stylesheet" href="/static/css/pattern_helper.css">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .console-output { background: #f5f5f5; padding: 10px; margin: 10px 0; font-family: monospace; }
        button { padding: 10px 20px; margin: 5px; }
    </style>
</head>
<body>
    <h1>Pattern Helper 测试页面</h1>
    
    <div class="test-section">
        <h2>测试API调用</h2>
        <button onclick="testAPICall()">测试 rainy 单词推荐</button>
        <div id="api-result" class="console-output"></div>
    </div>
    
    <div class="test-section">
        <h2>测试数据渲染</h2>
        <button onclick="testRenderLogic()">测试渲染逻辑</button>
        <div id="render-result" class="console-output"></div>
    </div>

    <div class="test-section">
        <h2>测试事件触发</h2>
        <button onclick="testWordChangedEvent()">手动触发 wordChanged 事件</button>
        <div id="event-result" class="console-output"></div>
    </div>

    <script>
        async function testAPICall() {
            const resultDiv = document.getElementById('api-result');
            resultDiv.innerHTML = '正在调用API...';
            
            try {
                const response = await fetch('/api/word_pattern_suggestions/64', {
                    method: 'GET',
                    credentials: 'same-origin'
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }
                
                const result = await response.json();
                
                resultDiv.innerHTML = `
                    <h3>API响应:</h3>
                    <pre>${JSON.stringify(result, null, 2)}</pre>
                    <h3>分析:</h3>
                    <p>成功: ${result.success}</p>
                    <p>有推荐: ${result.data?.has_recommendations}</p>
                    <p>推荐数量: ${result.data?.recommendations?.length || 0}</p>
                    <p>语言学专家推荐: ${result.data?.recommendations?.some(rec => rec.pattern_info?.is_linguistic_expert)}</p>
                `;
                
            } catch (error) {
                resultDiv.innerHTML = `<p style="color: red;">错误: ${error.message}</p>`;
            }
        }
        
        function testRenderLogic() {
            const resultDiv = document.getElementById('render-result');

            // 模拟API返回的数据
            const mockData = {
                word_id: 64,
                has_recommendations: true,
                recommendations: [
                    {
                        pattern_info: {
                            pattern_type: "suffix",
                            pattern_name: "-y后缀词汇",
                            linguistic_principle: "词缀学习法：相同后缀的词汇有相似的语法功能",
                            educational_value: "高价值：语法规律掌握",
                            is_linguistic_expert: true
                        },
                        similar_words: [
                            {
                                word_id: 295,
                                english_word: "photography",
                                chinese_meaning: "摄影",
                                similarity_score: 0.85,
                                learning_status: "review",
                                proficiency: 85.0
                            },
                            {
                                word_id: 688,
                                english_word: "happy",
                                chinese_meaning: "快乐的",
                                similarity_score: 0.85,
                                learning_status: "review",
                                proficiency: 75.0
                            }
                        ],
                        recommendation_reason: "这些单词都使用-y后缀，学习它们可以帮助你掌握形容词构成规律"
                    }
                ]
            };

            // 测试渲染逻辑
            const isLinguisticExpert = mockData.recommendations.some(rec =>
                rec.pattern_info && rec.pattern_info.is_linguistic_expert
            );

            resultDiv.innerHTML = `
                <h3>渲染逻辑测试:</h3>
                <p>有推荐: ${mockData.has_recommendations}</p>
                <p>推荐数量: ${mockData.recommendations.length}</p>
                <p>是语言学专家推荐: ${isLinguisticExpert}</p>
                <p>第一个推荐的pattern_info: ${JSON.stringify(mockData.recommendations[0].pattern_info, null, 2)}</p>
                <p>应该使用语言学专家渲染: ${isLinguisticExpert ? '是' : '否'}</p>
            `;
        }

        function testWordChangedEvent() {
            const resultDiv = document.getElementById('event-result');
            resultDiv.innerHTML = '正在触发事件...';

            // 手动触发 wordChanged 事件
            const wordData = {
                id: 64,
                english_word: 'rainy',
                chinese_meaning: '下雨',
                item_type: 'word',
                star_level: 1
            };

            document.dispatchEvent(new CustomEvent('wordChanged', {
                detail: wordData
            }));

            resultDiv.innerHTML = `
                <h3>事件触发测试:</h3>
                <p>已触发 wordChanged 事件</p>
                <p>单词数据: ${JSON.stringify(wordData, null, 2)}</p>
                <p>请查看浏览器控制台查看 Pattern Helper 的响应</p>
            `;
        }
    </script>

    <!-- 包含 Pattern Helper -->
    <script src="/static/js/pattern_helper.js"></script>
    <script src="/debug_pattern_helper.js"></script>
</body>
</html>
