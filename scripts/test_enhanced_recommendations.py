#!/usr/bin/env python3
"""
测试增强版语言学专家推荐系统
"""

import sys
import os
import json
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.services.enhanced_linguistic_recommender import (
    EnhancedLinguisticRecommenderService,
    SmartRecommendationContext
)

def test_enhanced_recommendations():
    """测试增强版推荐系统"""
    
    print("🚀 开始测试增强版语言学专家推荐系统...")
    
    try:
        # 初始化增强推荐服务
        recommender = EnhancedLinguisticRecommenderService()
        print("✅ 增强推荐服务初始化成功")
        
        # 测试单词列表
        test_words = [
            'apple',      # 基础词汇
            'beautiful',  # 形容词
            'running',    # 动词
            'happiness',  # 抽象名词
            'computer'    # 现代词汇
        ]
        
        print(f"\n📝 测试单词: {', '.join(test_words)}")
        
        for word in test_words:
            print(f"\n{'='*50}")
            print(f"🔍 测试单词: {word}")
            print(f"{'='*50}")
            
            try:
                # 创建推荐上下文
                context = SmartRecommendationContext(
                    user_id=1,
                    target_word_id=1,  # 简化测试
                    target_word=word,
                    target_meaning="测试含义",
                    user_proficiency_level='intermediate',
                    learned_words_count=50,
                    recent_errors=[],
                    learning_preferences={
                        'orthographic': 0.3,
                        'phonetic': 0.2,
                        'semantic': 0.3,
                        'morphological': 0.2
                    }
                )
                
                # 获取增强推荐 - 使用正确的方法名
                recommendations = recommender.get_linguistic_recommendations(
                    user_id=1,
                    word_id=1,
                    context='learning'
                )
                
                if recommendations:
                    print(f"✅ 获得 {len(recommendations)} 个推荐组")
                    
                    for i, rec in enumerate(recommendations, 1):
                        print(f"\n📋 推荐组 {i}: {rec.category}")
                        print(f"   📊 教育价值: {rec.educational_value}")
                        print(f"   📝 解释: {rec.explanation}")
                        print(f"   🔬 原理: {rec.linguistic_principle}")
                        print(f"   📚 相似词数量: {len(rec.similar_words)}")
                        
                        # 显示前3个相似词
                        for j, word_obj in enumerate(rec.similar_words[:3], 1):
                            print(f"      {j}. {word_obj.word} - {word_obj.meaning}")
                            print(f"         熟练度: {word_obj.proficiency_level}")
                            print(f"         状态: {word_obj.status}")
                else:
                    print("❌ 未获得推荐")
                    
                    # 测试回退推荐
                    print("🔄 尝试回退推荐...")
                    fallback_recs = recommender._get_fallback_recommendations(
                        word, context, 5
                    )
                    
                    if fallback_recs:
                        print(f"✅ 回退推荐成功，获得 {len(fallback_recs)} 个推荐")
                        for rec in fallback_recs:
                            print(f"   - {rec.word}: {rec.meaning}")
                    else:
                        print("❌ 回退推荐也失败")
                        
            except Exception as e:
                print(f"❌ 测试单词 {word} 时出错: {str(e)}")
                import traceback
                traceback.print_exc()
        
        print(f"\n{'='*60}")
        print("🎯 测试数据库统计")
        print(f"{'='*60}")
        
        # 简化的数据库统计
        print(f"📊 语言学标注统计:")
        print(f"   - 增强推荐服务已初始化")
        print(f"   - 数据库路径: {recommender.db_path}")
        print(f"   - 相似度阈值: {recommender._similarity_threshold}")
        print(f"   - 最大回退推荐数: {recommender._max_fallback_recommendations}")
        
        print(f"\n🎉 增强版推荐系统测试完成！")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

def test_recommendation_quality():
    """测试推荐质量"""
    
    print(f"\n{'='*60}")
    print("🔍 推荐质量测试")
    print(f"{'='*60}")
    
    try:
        recommender = EnhancedLinguisticRecommenderService()
        
        # 测试不同类型的单词
        quality_tests = [
            {
                'word': 'happy',
                'expected_categories': ['semantic', 'morphological'],
                'description': '基础形容词 - 应有语义和词法推荐'
            },
            {
                'word': 'unhappy',
                'expected_categories': ['prefix', 'semantic'],
                'description': '前缀词 - 应有前缀和语义推荐'
            },
            {
                'word': 'running',
                'expected_categories': ['morphological', 'semantic'],
                'description': '动词变形 - 应有词法和语义推荐'
            }
        ]
        
        for test in quality_tests:
            print(f"\n🧪 测试: {test['description']}")
            print(f"   单词: {test['word']}")
            
            context = SmartRecommendationContext(
                user_id=1,
                target_word_id=1,
                target_word=test['word'],
                target_meaning="测试",
                user_proficiency_level='intermediate',
                learned_words_count=50,
                recent_errors=[],
                learning_preferences={
                    'orthographic': 0.25,
                    'phonetic': 0.25,
                    'semantic': 0.25,
                    'morphological': 0.25
                }
            )
            
            recommendations = recommender.get_linguistic_recommendations(
                user_id=1, word_id=1, context='learning'
            )
            
            if recommendations:
                categories = [rec.category for rec in recommendations]
                print(f"   ✅ 获得推荐类别: {categories}")
                
                # 检查是否包含期望的类别
                found_expected = any(
                    expected in categories 
                    for expected in test['expected_categories']
                )
                
                if found_expected:
                    print(f"   ✅ 包含期望的推荐类别")
                else:
                    print(f"   ⚠️  未包含期望的推荐类别: {test['expected_categories']}")
            else:
                print(f"   ❌ 未获得推荐")
        
        print(f"\n✅ 推荐质量测试完成")
        
    except Exception as e:
        print(f"❌ 推荐质量测试出错: {str(e)}")
        return False
    
    return True

if __name__ == '__main__':
    print("🎯 增强版语言学专家推荐系统测试")
    print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    success = True
    
    # 基础功能测试
    if not test_enhanced_recommendations():
        success = False
    
    # 推荐质量测试
    if not test_recommendation_quality():
        success = False
    
    if success:
        print(f"\n🎉 所有测试通过！")
        print(f"\n📝 接下来可以:")
        print(f"   1. 启动应用测试前端界面")
        print(f"   2. 测试新的反馈API端点")
        print(f"   3. 收集真实用户反馈")
    else:
        print(f"\n❌ 部分测试失败，请检查错误信息")
        sys.exit(1)
