#!/usr/bin/env python3
"""
彻底移除学习集群功能 - 数据库清理脚本
清空word_linguistic_annotations表中的learning_clusters数据
"""

import sys
import os
import sqlite3
import json
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def clean_learning_clusters():
    """清空数据库中的学习集群数据"""
    print("🗑️ 开始清理学习集群数据...")
    
    try:
        conn = sqlite3.connect('instance/words.db')
        cursor = conn.cursor()
        
        # 首先统计当前数据
        cursor.execute("""
            SELECT COUNT(*) as total,
                   SUM(CASE WHEN learning_clusters != '[]' AND learning_clusters IS NOT NULL THEN 1 ELSE 0 END) as with_clusters
            FROM word_linguistic_annotations
        """)
        
        before_stats = cursor.fetchone()
        total_words, words_with_clusters = before_stats
        
        print(f"📊 清理前统计:")
        print(f"   - 总标注数: {total_words}")
        print(f"   - 含学习集群: {words_with_clusters}")
        
        if words_with_clusters == 0:
            print("✅ 数据库中没有学习集群数据，无需清理")
            return True
        
        # 清空learning_clusters字段
        print(f"🧹 正在清理 {words_with_clusters} 条学习集群数据...")
        
        cursor.execute("""
            UPDATE word_linguistic_annotations 
            SET learning_clusters = '[]',
                updated_at = ?
            WHERE learning_clusters != '[]' AND learning_clusters IS NOT NULL
        """, (datetime.now().strftime('%Y-%m-%d %H:%M:%S'),))
        
        affected_rows = cursor.rowcount
        conn.commit()
        
        # 验证清理结果
        cursor.execute("""
            SELECT COUNT(*) as total,
                   SUM(CASE WHEN learning_clusters != '[]' AND learning_clusters IS NOT NULL THEN 1 ELSE 0 END) as with_clusters
            FROM word_linguistic_annotations
        """)
        
        after_stats = cursor.fetchone()
        total_after, clusters_after = after_stats
        
        print(f"📊 清理后统计:")
        print(f"   - 总标注数: {total_after}")
        print(f"   - 含学习集群: {clusters_after}")
        print(f"   - 已清理数量: {affected_rows}")
        
        if clusters_after == 0:
            print("✅ 学习集群数据清理完成！")
            return True
        else:
            print(f"❌ 清理不完整，仍有 {clusters_after} 条学习集群数据")
            return False
            
    except Exception as e:
        print(f"❌ 清理失败: {e}")
        return False
    finally:
        if 'conn' in locals():
            conn.close()

def show_sample_data():
    """显示清理前后的样本数据"""
    print("\n🔍 显示样本数据...")
    
    try:
        conn = sqlite3.connect('instance/words.db')
        cursor = conn.cursor()
        
        # 查找一些有代表性的词汇
        cursor.execute("""
            SELECT w.english_word, wla.learning_clusters, wla.linguistic_tags
            FROM word_linguistic_annotations wla
            JOIN word w ON wla.word_id = w.id
            WHERE w.english_word IN ('rainy', 'sunny', 'happy', 'computer', 'photography')
            ORDER BY w.english_word
        """)
        
        samples = cursor.fetchall()
        
        print("📝 样本词汇状态:")
        for word, clusters, tags in samples:
            clusters_data = json.loads(clusters) if clusters else []
            tags_data = json.loads(tags) if tags else []
            
            print(f"   {word}:")
            print(f"     - 学习集群: {clusters_data}")
            print(f"     - 语言标签: {tags_data}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 显示样本数据失败: {e}")

def backup_data():
    """备份学习集群数据（可选）"""
    print("\n💾 创建学习集群数据备份...")
    
    try:
        conn = sqlite3.connect('instance/words.db')
        cursor = conn.cursor()
        
        # 导出有学习集群的数据
        cursor.execute("""
            SELECT w.english_word, wla.learning_clusters
            FROM word_linguistic_annotations wla
            JOIN word w ON wla.word_id = w.id
            WHERE wla.learning_clusters != '[]' AND wla.learning_clusters IS NOT NULL
            ORDER BY w.english_word
        """)
        
        backup_data = cursor.fetchall()
        
        if backup_data:
            backup_file = f"scripts/backup_learning_clusters_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            
            backup_content = {}
            for word, clusters in backup_data:
                backup_content[word] = json.loads(clusters) if clusters else []
            
            with open(backup_file, 'w', encoding='utf-8') as f:
                json.dump(backup_content, f, ensure_ascii=False, indent=2)
            
            print(f"✅ 备份已保存到: {backup_file}")
            print(f"📊 备份了 {len(backup_data)} 个词汇的学习集群数据")
        else:
            print("ℹ️  没有学习集群数据需要备份")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 备份失败: {e}")

def main():
    """主函数"""
    print("🗑️ 学习集群功能移除 - 数据库清理")
    print("=" * 50)
    
    # 显示清理前的样本数据
    show_sample_data()
    
    # 创建备份
    backup_data()
    
    # 执行清理
    success = clean_learning_clusters()
    
    # 显示清理后的样本数据
    show_sample_data()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 学习集群数据清理成功！")
        print("\n✅ 清理效果:")
        print("   - 数据库中的学习集群数据已清空")
        print("   - 所有learning_clusters字段已设置为'[]'")
        print("   - 原始数据已备份")
        print("\n📝 下一步:")
        print("   - 修改推荐服务代码")
        print("   - 更新配置文件")
        print("   - 修改前端显示逻辑")
        return True
    else:
        print("❌ 学习集群数据清理失败！")
        print("请检查错误信息并重试")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
