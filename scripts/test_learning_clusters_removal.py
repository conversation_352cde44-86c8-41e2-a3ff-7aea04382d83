#!/usr/bin/env python3
"""
学习集群功能移除验证测试
验证所有学习集群相关功能已完全移除
"""

import sys
import os
import sqlite3
import json

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_database_cleanup():
    """测试数据库中的学习集群数据清理"""
    print("🔍 测试数据库学习集群清理...")
    
    try:
        conn = sqlite3.connect('instance/words.db')
        cursor = conn.cursor()
        
        # 检查学习集群数据
        cursor.execute("""
            SELECT COUNT(*) as total,
                   SUM(CASE WHEN learning_clusters != '[]' AND learning_clusters IS NOT NULL THEN 1 ELSE 0 END) as with_clusters
            FROM word_linguistic_annotations
        """)
        
        result = cursor.fetchone()
        total, with_clusters = result
        
        print(f"   📊 数据库统计:")
        print(f"      - 总标注数: {total}")
        print(f"      - 含学习集群: {with_clusters}")
        
        if with_clusters == 0:
            print(f"      ✅ 学习集群数据已完全清除")
            return True
        else:
            print(f"      ❌ 仍有 {with_clusters} 条学习集群数据")
            return False
            
    except Exception as e:
        print(f"   ❌ 数据库测试失败: {e}")
        return False
    finally:
        if 'conn' in locals():
            conn.close()

def test_recommendation_services():
    """测试推荐服务"""
    print("\n🔍 测试推荐服务...")
    
    try:
        from src.services.linguistic_recommender import LinguisticRecommenderService
        
        service = LinguisticRecommenderService('instance/words.db')
        
        # 测试rainy的推荐（应该没有学习集群推荐）
        recommendations = service.get_linguistic_recommendations(
            user_id=1, 
            word_id=64,  # rainy
            context='learning'
        )
        
        print(f"   📊 推荐服务测试:")
        print(f"      - rainy推荐数量: {len(recommendations)}")
        
        # 检查是否还有学习集群推荐
        cluster_recs = [r for r in recommendations if 'cluster_' in r.category]
        
        if cluster_recs:
            print(f"      ❌ 仍有学习集群推荐: {len(cluster_recs)} 个")
            for rec in cluster_recs:
                print(f"         - {rec.category}: {rec.category_name}")
            return False
        else:
            print(f"      ✅ 学习集群推荐已移除")
            
        # 显示剩余推荐类型
        if recommendations:
            print(f"      📝 剩余推荐类型:")
            for rec in recommendations:
                print(f"         - {rec.category}: {rec.category_name}")
        else:
            print(f"      ℹ️  当前无推荐（可能因为用户未学习相关词汇）")
            
        return True
        
    except Exception as e:
        print(f"   ❌ 推荐服务测试失败: {e}")
        return False

def test_enhanced_service():
    """测试增强推荐服务"""
    print("\n🔍 测试增强推荐服务...")
    
    try:
        from src.services.enhanced_linguistic_recommender import EnhancedLinguisticRecommenderService
        
        service = EnhancedLinguisticRecommenderService('instance/words.db')
        
        # 测试推荐功能
        recommendations = service.get_linguistic_recommendations(
            user_id=1,
            word_id=64,  # rainy
            context='learning'
        )
        
        print(f"   📊 增强推荐服务测试:")
        print(f"      - rainy推荐数量: {len(recommendations)}")
        
        # 检查是否还有学习集群推荐
        cluster_recs = [r for r in recommendations if 'cluster_' in r.category]
        
        if cluster_recs:
            print(f"      ❌ 仍有学习集群推荐: {len(cluster_recs)} 个")
            return False
        else:
            print(f"      ✅ 增强服务中无学习集群推荐")
            
        return True
        
    except Exception as e:
        print(f"   ❌ 增强推荐服务测试失败: {e}")
        return False

def test_configuration():
    """测试配置文件"""
    print("\n🔍 测试配置文件...")
    
    try:
        from src.services.pattern.recommendation_config import RecommendationConfig
        
        config = RecommendationConfig()
        
        # 检查配置属性
        config_dict = config.__dict__
        
        # 查找学习集群相关配置（应该没有）
        cluster_keys = [k for k in config_dict.keys() if 'cluster' in k.lower()]
        
        print(f"   📊 配置检查:")
        print(f"      - 总配置项: {len(config_dict)}")
        print(f"      - 学习集群相关: {len(cluster_keys)}")
        
        if cluster_keys:
            print(f"      ❌ 仍有学习集群配置: {cluster_keys}")
            return False
        else:
            print(f"      ✅ 学习集群配置已移除")
            
        # 显示剩余配置
        print(f"      📝 当前配置:")
        for key, value in config_dict.items():
            print(f"         - {key}: {value}")
            
        return True
        
    except Exception as e:
        print(f"   ❌ 配置测试失败: {e}")
        return False

def test_sample_words():
    """测试样本词汇的推荐情况"""
    print("\n🔍 测试样本词汇推荐...")
    
    sample_words = ['rainy', 'sunny', 'happy', 'computer']
    
    try:
        from src.services.linguistic_recommender import LinguisticRecommenderService
        
        service = LinguisticRecommenderService('instance/words.db')
        
        for word in sample_words:
            # 获取词汇ID
            conn = sqlite3.connect('instance/words.db')
            cursor = conn.cursor()
            cursor.execute('SELECT id FROM word WHERE english_word = ?', (word,))
            result = cursor.fetchone()
            conn.close()
            
            if result:
                word_id = result[0]
                recommendations = service.get_linguistic_recommendations(
                    user_id=1, 
                    word_id=word_id, 
                    context='learning'
                )
                
                cluster_recs = [r for r in recommendations if 'cluster_' in r.category]
                
                print(f"   {word}: {len(recommendations)} 推荐, {len(cluster_recs)} 学习集群推荐")
                
                if cluster_recs:
                    print(f"      ❌ 仍有学习集群推荐")
                    return False
        
        print(f"   ✅ 所有样本词汇无学习集群推荐")
        return True
        
    except Exception as e:
        print(f"   ❌ 样本词汇测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🗑️ 学习集群功能移除验证测试")
    print("=" * 50)
    
    tests = [
        ("数据库清理", test_database_cleanup),
        ("推荐服务", test_recommendation_services),
        ("增强推荐服务", test_enhanced_service),
        ("配置文件", test_configuration),
        ("样本词汇", test_sample_words)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            status = "✅ 通过" if result else "❌ 失败"
            print(f"\n{test_name}: {status}")
            if result:
                passed += 1
        except Exception as e:
            print(f"\n{test_name}: ❌ 异常 - {e}")
    
    print("\n" + "=" * 50)
    print(f"🎯 测试结果: {passed}/{total} 项通过")
    
    if passed == total:
        print("🎉 学习集群功能已完全移除！")
        print("\n✅ 移除效果确认:")
        print("   - 数据库中的学习集群数据已清空")
        print("   - 推荐服务中的学习集群推荐逻辑已移除")
        print("   - 配置文件中的学习集群相关设置已禁用")
        print("   - 前端显示逻辑中的学习集群图标已移除")
        print("   - 系统运行正常，无学习集群功能")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关组件")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
