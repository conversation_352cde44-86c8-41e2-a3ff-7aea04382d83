#!/usr/bin/env python3
"""
调试rainy推荐功能
详细分析为什么rainy没有推荐
"""

import sys
import os
import sqlite3
import json

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def debug_rainy_recommendations():
    """调试rainy的推荐功能"""
    print("🔍 调试rainy推荐功能")
    print("=" * 50)
    
    try:
        from src.services.linguistic_recommender import LinguisticRecommenderService
        
        service = LinguisticRecommenderService('instance/words.db')
        
        # 获取rainy的词汇ID
        conn = sqlite3.connect('instance/words.db')
        cursor = conn.cursor()
        cursor.execute('SELECT id FROM word WHERE english_word = ?', ('rainy',))
        result = cursor.fetchone()
        
        if not result:
            print("❌ 找不到rainy词汇")
            return False
            
        word_id = result[0]
        print(f"📝 rainy词汇ID: {word_id}")
        
        # 获取rainy的标注
        target_annotation = service._get_word_annotation(word_id)
        print(f"📊 rainy的标注: {target_annotation}")
        
        # 获取用户词汇
        user_words = service._get_user_learned_words(1)
        print(f"📚 用户词汇数量: {len(user_words)}")
        
        # 检查后缀推荐
        print(f"\n🔍 检查后缀推荐...")
        suffix_words = []
        target_suffix = target_annotation.get('suffix')
        print(f"   rainy的后缀: {target_suffix}")
        
        if target_suffix:
            for word in user_words:
                if (word.get('has_annotation') and 
                    word.get('suffix') == target_suffix and
                    word['word_id'] != word_id):
                    suffix_words.append(word)
                    print(f"   找到相同后缀词汇: {word['english_word']} (后缀: {word.get('suffix')})")
        
        print(f"   相同后缀词汇数量: {len(suffix_words)}")
        print(f"   最小要求: {service._min_words_per_category}")
        
        # 检查语音推荐
        print(f"\n🔍 检查语音推荐...")
        phonetic_words = []
        target_tags = target_annotation.get('linguistic_tags', [])
        print(f"   rainy的语言学标签: {target_tags}")
        
        phonetic_tags = [tag for tag in target_tags if any(pattern in tag for pattern in ['sound', 'vowel', 'consonant', 'phonetic'])]
        print(f"   语音相关标签: {phonetic_tags}")
        
        for word in user_words:
            if word.get('has_annotation') and word['word_id'] != word_id:
                word_tags = word.get('linguistic_tags', [])
                common_phonetic = set(phonetic_tags) & set(word_tags)
                if common_phonetic:
                    phonetic_words.append(word)
                    print(f"   找到相同语音特征词汇: {word['english_word']} (共同标签: {list(common_phonetic)})")
        
        print(f"   相同语音特征词汇数量: {len(phonetic_words)}")
        
        # 检查词根推荐
        print(f"\n🔍 检查词根推荐...")
        morphology_words = []
        target_root = target_annotation.get('root')
        print(f"   rainy的词根: {target_root}")
        
        if target_root:
            for word in user_words:
                if (word.get('has_annotation') and 
                    word.get('root') == target_root and
                    word['word_id'] != word_id):
                    morphology_words.append(word)
                    print(f"   找到相同词根词汇: {word['english_word']} (词根: {word.get('root')})")
        
        print(f"   相同词根词汇数量: {len(morphology_words)}")
        
        # 检查对比推荐
        print(f"\n🔍 检查对比推荐...")
        contrast_words = []
        contrast_tags = [tag for tag in target_tags if 'opposite' in tag or 'contrast' in tag or 'antonym' in tag]
        print(f"   对比相关标签: {contrast_tags}")
        
        for word in user_words:
            if word.get('has_annotation') and word['word_id'] != word_id:
                word_tags = word.get('linguistic_tags', [])
                common_contrast = set(contrast_tags) & set(word_tags)
                if common_contrast:
                    contrast_words.append(word)
                    print(f"   找到对比词汇: {word['english_word']} (共同标签: {list(common_contrast)})")
        
        print(f"   对比词汇数量: {len(contrast_words)}")
        
        # 总结
        print(f"\n📊 推荐分析总结:")
        print(f"   - 后缀推荐: {len(suffix_words)} 个词汇 ({'✅ 足够' if len(suffix_words) >= service._min_words_per_category else '❌ 不足'})")
        print(f"   - 语音推荐: {len(phonetic_words)} 个词汇 ({'✅ 足够' if len(phonetic_words) >= service._min_words_per_category else '❌ 不足'})")
        print(f"   - 词根推荐: {len(morphology_words)} 个词汇 ({'✅ 足够' if len(morphology_words) >= service._min_words_per_category else '❌ 不足'})")
        print(f"   - 对比推荐: {len(contrast_words)} 个词汇 ({'✅ 足够' if len(contrast_words) >= service._min_words_per_category else '❌ 不足'})")
        
        # 建议
        print(f"\n💡 建议:")
        if len(suffix_words) < service._min_words_per_category:
            print(f"   - 添加更多-y后缀的形容词（如：sunny, windy, cloudy, snowy等）")
        if len(phonetic_words) < service._min_words_per_category:
            print(f"   - 添加更多包含ai音的词汇")
        if len(morphology_words) < service._min_words_per_category:
            print(f"   - 添加更多rain词根的词汇（如：rain, rainbow等）")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    success = debug_rainy_recommendations()
    
    if success:
        print("\n🎉 rainy推荐调试完成！")
    else:
        print("\n❌ rainy推荐调试失败")
    
    return success

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
