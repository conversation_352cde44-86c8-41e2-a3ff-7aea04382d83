#!/usr/bin/env python3
"""
高级版本：使用 Hugging Face Diffusers 生成单词 "flower" 的图片
支持多种模型和备选方案
"""

import os
import sys
import torch
from pathlib import Path
from PIL import Image, ImageDraw
import random
import math

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def check_gpu_availability():
    """检查GPU可用性"""
    if torch.cuda.is_available():
        print(f"✅ GPU 可用: {torch.cuda.get_device_name(0)}")
        memory_gb = torch.cuda.get_device_properties(0).total_memory / 1024**3
        print(f"   GPU 内存: {memory_gb:.1f} GB")
        return "cuda"
    else:
        print("⚠️  GPU 不可用，将使用 CPU（速度较慢）")
        return "cpu"


def test_network_connection():
    """测试网络连接"""
    try:
        import urllib.request
        urllib.request.urlopen('https://huggingface.co', timeout=10)
        return True
    except:
        return False


def create_simple_flower_image():
    """创建简单的花朵图片（备选方案）"""
    print("🎨 创建简单的花朵图片...")
    
    # 创建输出目录
    output_dir = project_root / "static" / "images" / "generated"
    output_dir.mkdir(parents=True, exist_ok=True)
    
    learning_dir = project_root / "static" / "images" / "words"
    learning_dir.mkdir(parents=True, exist_ok=True)
    
    # 创建学习用花朵图片
    width, height = 512, 512
    image = Image.new('RGB', (width, height), (255, 255, 255))
    draw = ImageDraw.Draw(image)
    
    center_x, center_y = width // 2, height // 2 - 50
    
    # 绘制花茎
    draw.rectangle([center_x - 8, center_y + 80, center_x + 8, height - 30], 
                  fill=(34, 139, 34))
    
    # 绘制叶子
    leaf_points = [
        (center_x - 40, center_y + 100),
        (center_x - 70, center_y + 130),
        (center_x - 30, center_y + 160),
        (center_x - 15, center_y + 130)
    ]
    draw.polygon(leaf_points, fill=(50, 150, 50), outline=(30, 100, 30))
    
    # 绘制花瓣
    petal_color = (220, 20, 60)
    petal_radius = 80
    num_petals = 6
    
    for j in range(num_petals):
        angle = (2 * math.pi * j) / num_petals
        petal_x = center_x + int(petal_radius * 0.6 * math.cos(angle))
        petal_y = center_y + int(petal_radius * 0.6 * math.sin(angle))
        
        draw.ellipse([
            petal_x - 35, petal_y - 50,
            petal_x + 35, petal_y + 50
        ], fill=petal_color, outline=(150, 0, 30), width=2)
    
    # 绘制花心
    draw.ellipse([
        center_x - 25, center_y - 25,
        center_x + 25, center_y + 25
    ], fill=(255, 215, 0), outline=(200, 165, 0), width=2)
    
    # 保存学习用图片
    learning_filepath = learning_dir / "flower.jpg"
    image.save(learning_filepath, quality=95)
    print(f"   ✅ 学习用图片保存: {learning_filepath}")
    
    return True


def generate_with_diffusers():
    """使用 Diffusers 生成高质量花朵图片"""
    try:
        from diffusers import AutoPipelineForText2Image
        
        print("📥 加载 Stable Diffusion 模型...")
        device = check_gpu_availability()
        
        # 尝试使用较小的模型
        model_options = [
            "stable-diffusion-v1-5/stable-diffusion-v1-5",
            "runwayml/stable-diffusion-v1-5",
            "CompVis/stable-diffusion-v1-4"
        ]
        
        pipeline = None
        for model_name in model_options:
            try:
                print(f"   尝试加载模型: {model_name}")
                pipeline = AutoPipelineForText2Image.from_pretrained(
                    model_name,
                    torch_dtype=torch.float16 if device == "cuda" else torch.float32,
                    variant="fp16" if device == "cuda" else None,
                    use_safetensors=True
                )
                print(f"   ✅ 成功加载: {model_name}")
                break
            except Exception as e:
                print(f"   ❌ 加载失败: {e}")
                continue
        
        if pipeline is None:
            print("❌ 无法加载任何 Diffusion 模型")
            return False
        
        pipeline = pipeline.to(device)
        
        # 启用内存优化
        if device == "cuda":
            pipeline.enable_model_cpu_offload()
            try:
                pipeline.enable_xformers_memory_efficient_attention()
                print("✅ 启用了 xFormers 内存优化")
            except:
                print("⚠️  xFormers 未安装，跳过内存优化")
        
        # 生成图片
        prompts = [
            "A beautiful red flower with detailed petals, photorealistic, high quality",
            "A vibrant flower in full bloom, macro photography, stunning details",
            "A simple elegant flower, clean background, perfect for learning"
        ]
        
        negative_prompt = "blurry, low quality, distorted, ugly, bad anatomy"
        
        # 创建输出目录
        output_dir = project_root / "static" / "images" / "generated"
        output_dir.mkdir(parents=True, exist_ok=True)
        
        learning_dir = project_root / "static" / "images" / "words"
        learning_dir.mkdir(parents=True, exist_ok=True)
        
        print("🎨 开始生成高质量图片...")
        
        for i, prompt in enumerate(prompts, 1):
            print(f"   生成图片 {i}/{len(prompts)}: {prompt[:50]}...")
            
            generator = torch.Generator(device=device).manual_seed(42 + i)
            
            result = pipeline(
                prompt=prompt,
                negative_prompt=negative_prompt,
                num_inference_steps=30,  # 减少步数以加快速度
                guidance_scale=7.5,
                width=512,
                height=512,
                generator=generator
            )
            
            image = result.images[0]
            
            if i == len(prompts):  # 最后一张作为学习用图片
                filepath = learning_dir / "flower.jpg"
                print(f"   ✅ 学习用图片保存: {filepath}")
            else:
                filepath = output_dir / f"flower_ai_{i}.jpg"
                print(f"   ✅ 保存: {filepath}")
            
            image.save(filepath, quality=95)
        
        print("🎉 AI 图片生成完成！")
        return True
        
    except Exception as e:
        print(f"❌ Diffusers 生成失败: {str(e)}")
        return False


def main():
    """主函数"""
    print("🌸 高级花朵图片生成器")
    print("=" * 50)
    
    # 检查网络连接
    print("🌐 检查网络连接...")
    has_network = test_network_connection()
    
    if has_network:
        print("✅ 网络连接正常")
        
        # 检查依赖
        try:
            import diffusers
            print(f"✅ Diffusers 版本: {diffusers.__version__}")
            
            # 尝试使用 AI 生成
            print("\n🤖 尝试使用 AI 模型生成...")
            ai_success = generate_with_diffusers()
            
            if ai_success:
                print("\n🎯 AI 生成成功！")
                return True
            else:
                print("\n⚠️  AI 生成失败，使用备选方案...")
                
        except ImportError:
            print("❌ 未安装 diffusers 库")
            print("请运行: pip install diffusers transformers accelerate")
            print("使用备选方案...")
    else:
        print("❌ 网络连接失败，使用备选方案...")
    
    # 使用备选方案
    print("\n🎨 使用简单绘图方案...")
    simple_success = create_simple_flower_image()
    
    if simple_success:
        print("\n🎯 使用建议:")
        print("1. 查看生成的图片质量")
        print("2. 如需 AI 生成，请确保网络连接和依赖安装")
        print("3. 生成的图片可直接用于单词学习应用")
        return True
    
    return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
