#!/usr/bin/env python3
"""
测试rainy词汇的推荐功能
验证移除学习集群后，rainy仍能正常生成推荐
"""

import sys
import os
import sqlite3

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_rainy_recommendations():
    """测试rainy的推荐功能"""
    print("🌧️ 测试rainy词汇推荐功能")
    print("=" * 50)
    
    try:
        from src.services.linguistic_recommender import LinguisticRecommenderService
        
        service = LinguisticRecommenderService('instance/words.db')
        
        # 获取rainy的词汇ID
        conn = sqlite3.connect('instance/words.db')
        cursor = conn.cursor()
        cursor.execute('SELECT id FROM word WHERE english_word = ?', ('rainy',))
        result = cursor.fetchone()
        
        if not result:
            print("❌ 找不到rainy词汇")
            return False
            
        word_id = result[0]
        print(f"📝 rainy词汇ID: {word_id}")
        
        # 检查rainy的语言学标注
        cursor.execute("""
            SELECT linguistic_tags, morphology_type, has_true_prefix, prefix, suffix
            FROM word_linguistic_annotations 
            WHERE word_id = ?
        """, (word_id,))
        
        annotation = cursor.fetchone()
        if annotation:
            tags, morphology, has_prefix, prefix, suffix = annotation
            print(f"📊 rainy的语言学标注:")
            print(f"   - 语言学标签: {tags}")
            print(f"   - 词法类型: {morphology}")
            print(f"   - 有真前缀: {has_prefix}")
            print(f"   - 前缀: {prefix}")
            print(f"   - 后缀: {suffix}")
        else:
            print("⚠️  rainy没有语言学标注")
        
        conn.close()
        
        # 测试推荐功能
        print(f"\n🔍 获取rainy的推荐...")
        recommendations = service.get_linguistic_recommendations(
            user_id=1, 
            word_id=word_id, 
            context='learning'
        )
        
        print(f"📊 推荐结果:")
        print(f"   - 推荐数量: {len(recommendations)}")
        
        if recommendations:
            for i, rec in enumerate(recommendations, 1):
                print(f"\n   {i}. {rec.category_name} ({rec.category})")
                print(f"      📝 解释: {rec.explanation}")
                print(f"      🎯 语言学原理: {rec.linguistic_principle}")
                print(f"      📚 教育价值: {rec.educational_value}")
                print(f"      🔗 相似词汇: {len(rec.similar_words)} 个")
                
                for j, word in enumerate(rec.similar_words[:3], 1):  # 只显示前3个
                    print(f"         {j}. {word.english_word} - {word.chinese_meaning}")
                    print(f"            相似原因: {word.similarity_reason}")
                    print(f"            置信度: {word.confidence:.2f}")
        else:
            print("   ℹ️  当前无推荐")
            print("   💡 可能原因:")
            print("      - 用户还没有学习足够的相关词汇")
            print("      - 需要添加更多用户词汇数据")
            
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def add_sample_user_words():
    """添加一些样本用户词汇以便测试推荐"""
    print("\n📚 添加样本用户词汇...")
    
    try:
        conn = sqlite3.connect('instance/words.db')
        cursor = conn.cursor()
        
        # 获取一些相关词汇的ID
        sample_words = ['rain', 'sunny', 'cloudy', 'windy', 'snowy', 'happy', 'angry', 'funny', 'easy', 'heavy']
        
        for word in sample_words:
            cursor.execute('SELECT id FROM word WHERE english_word = ?', (word,))
            result = cursor.fetchone()
            
            if result:
                word_id = result[0]
                # 检查是否已存在用户词汇记录
                cursor.execute('SELECT id FROM user_word WHERE user_id = 1 AND word_id = ?', (word_id,))
                existing = cursor.fetchone()
                
                if not existing:
                    cursor.execute("""
                        INSERT INTO user_word (user_id, word_id, status, proficiency, last_learning_date)
                        VALUES (1, ?, 'learning', 0.7, CURRENT_TIMESTAMP)
                    """, (word_id,))
                    print(f"   ✅ 添加用户词汇: {word}")
        
        conn.commit()
        conn.close()
        print("   📊 样本用户词汇添加完成")
        return True
        
    except Exception as e:
        print(f"   ❌ 添加样本词汇失败: {e}")
        return False

def main():
    """主函数"""
    # 先添加样本用户词汇
    add_sample_user_words()
    
    # 测试rainy推荐
    success = test_rainy_recommendations()
    
    if success:
        print("\n🎉 rainy推荐功能测试完成！")
        print("✅ 学习集群移除后，推荐系统仍能正常工作")
    else:
        print("\n❌ rainy推荐功能测试失败")
    
    return success

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
