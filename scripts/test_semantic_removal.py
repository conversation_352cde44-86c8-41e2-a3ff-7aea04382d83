#!/usr/bin/env python3
"""
测试语义组功能移除效果
验证系统是否已经完全移除语义组相关功能
"""

import sys
import os
import requests
import json
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.services.linguistic_recommender import LinguisticRecommenderService
from src.services.enhanced_linguistic_recommender import EnhancedLinguisticRecommenderService

def test_database_cleanup():
    """测试数据库清理效果"""
    print("🔍 测试数据库语义组清理效果...")
    
    try:
        service = LinguisticRecommenderService('instance/words.db')
        stats = service.get_linguistic_statistics()
        
        print(f"   📊 数据库统计:")
        print(f"      - 总单词数: {stats.get('total_words', 0)}")
        print(f"      - 已标注单词: {stats.get('total_annotated', 0)}")
        print(f"      - 标注覆盖率: {stats.get('annotation_rate', 0)}%")
        
        # 检查是否还有语义组统计
        if 'top_semantic_groups' in stats:
            print(f"      ⚠️  仍有语义组统计: {stats['top_semantic_groups']}")
            return False
        else:
            print(f"      ✅ 语义组统计已移除")
            return True
            
    except Exception as e:
        print(f"   ❌ 数据库测试失败: {e}")
        return False

def test_recommendation_service():
    """测试推荐服务"""
    print("\n🔍 测试推荐服务语义功能移除...")
    
    try:
        service = LinguisticRecommenderService('instance/words.db')
        
        # 测试推荐功能
        recommendations = service.get_linguistic_recommendations(
            user_id=1, 
            word_id=1, 
            context='learning'
        )
        
        print(f"   📊 推荐结果:")
        print(f"      - 推荐数量: {len(recommendations)}")
        
        # 检查是否还有语义推荐
        semantic_recs = [r for r in recommendations if 'semantic' in r.category]
        
        if semantic_recs:
            print(f"      ⚠️  仍有语义推荐: {len(semantic_recs)} 个")
            for rec in semantic_recs:
                print(f"         - {rec.category}: {rec.category_name}")
            return False
        else:
            print(f"      ✅ 语义推荐已完全移除")
            
        # 显示剩余推荐类型
        if recommendations:
            print(f"      📝 剩余推荐类型:")
            for rec in recommendations:
                print(f"         - {rec.category}: {rec.category_name}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 推荐服务测试失败: {e}")
        return False

def test_enhanced_service():
    """测试增强推荐服务"""
    print("\n🔍 测试增强推荐服务语义功能移除...")
    
    try:
        service = EnhancedLinguisticRecommenderService('instance/words.db')
        
        # 测试推荐功能
        recommendations = service.get_linguistic_recommendations(
            user_id=1, 
            word_id=1, 
            context='learning'
        )
        
        print(f"   📊 增强推荐结果:")
        print(f"      - 推荐数量: {len(recommendations)}")
        
        # 检查是否还有语义推荐
        semantic_recs = [r for r in recommendations if 'semantic' in r.category]
        
        if semantic_recs:
            print(f"      ⚠️  仍有语义推荐: {len(semantic_recs)} 个")
            return False
        else:
            print(f"      ✅ 语义推荐已完全移除")
            
        return True
        
    except Exception as e:
        print(f"   ❌ 增强推荐服务测试失败: {e}")
        return False

def test_api_endpoints():
    """测试API端点"""
    print("\n🔍 测试API端点语义功能移除...")
    
    base_url = "http://127.0.0.1:5007"
    
    try:
        # 测试推荐配置API
        response = requests.get(f"{base_url}/api/recommendation/config/1")
        
        if response.status_code == 200:
            config = response.json()['data']['config']
            print(f"   📊 推荐配置:")
            
            # 检查语义相关配置
            semantic_keys = [k for k in config.keys() if 'semantic' in k.lower()]
            
            if semantic_keys:
                print(f"      ⚠️  仍有语义配置: {semantic_keys}")
                for key in semantic_keys:
                    print(f"         - {key}: {config[key]}")
                return False
            else:
                print(f"      ✅ 语义配置已移除")
                
            # 显示剩余配置
            print(f"      📝 剩余配置项:")
            for key, value in config.items():
                print(f"         - {key}: {value}")
                
            return True
        else:
            print(f"   ❌ API请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ API测试失败: {e}")
        return False

def test_pattern_recommendations():
    """测试Pattern推荐"""
    print("\n🔍 测试Pattern推荐语义功能移除...")
    
    base_url = "http://127.0.0.1:5007"
    
    try:
        # 测试Pattern推荐API
        response = requests.get(f"{base_url}/api/pattern/recommendations/1")
        
        if response.status_code == 200:
            data = response.json()['data']
            recommendations = data.get('recommendations', [])
            
            print(f"   📊 Pattern推荐结果:")
            print(f"      - 推荐数量: {len(recommendations)}")
            
            # 检查是否还有语义推荐
            semantic_recs = []
            for rec in recommendations:
                if 'semantic' in rec.get('pattern_info', {}).get('pattern_name', '').lower():
                    semantic_recs.append(rec)
            
            if semantic_recs:
                print(f"      ⚠️  仍有语义Pattern推荐: {len(semantic_recs)} 个")
                return False
            else:
                print(f"      ✅ 语义Pattern推荐已移除")
                
            return True
        else:
            print(f"   ❌ Pattern API请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Pattern推荐测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🗑️ 语义组功能移除验证测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    test_results = []
    
    # 执行各项测试
    test_results.append(("数据库清理", test_database_cleanup()))
    test_results.append(("推荐服务", test_recommendation_service()))
    test_results.append(("增强推荐服务", test_enhanced_service()))
    test_results.append(("API端点", test_api_endpoints()))
    test_results.append(("Pattern推荐", test_pattern_recommendations()))
    
    # 汇总测试结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总:")
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 语义组功能已完全移除！")
        print("\n📝 移除效果:")
        print("   ✅ 数据库中的语义组数据已清空")
        print("   ✅ 推荐服务中的语义推荐逻辑已移除")
        print("   ✅ 配置文件中的语义相关设置已禁用")
        print("   ✅ API端点不再返回语义相关数据")
        print("   ✅ 系统运行正常，无语义组功能")
        return True
    else:
        print("⚠️  语义组功能移除不完整，请检查失败的测试项")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
