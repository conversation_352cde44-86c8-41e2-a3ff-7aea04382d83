#!/usr/bin/env python3
"""
简化的语义组移除测试
"""

import sys
import os
import sqlite3

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_database():
    """测试数据库中的语义组数据"""
    print("🔍 测试数据库语义组清理...")
    
    try:
        conn = sqlite3.connect('instance/words.db')
        cursor = conn.cursor()
        
        # 检查语义组数据
        cursor.execute("""
            SELECT COUNT(*) as total,
                   SUM(CASE WHEN semantic_groups != '[]' AND semantic_groups IS NOT NULL THEN 1 ELSE 0 END) as with_semantic
            FROM word_linguistic_annotations
        """)
        
        result = cursor.fetchone()
        total, with_semantic = result
        
        print(f"   📊 数据库统计:")
        print(f"      - 总标注数: {total}")
        print(f"      - 含语义组: {with_semantic}")
        
        if with_semantic == 0:
            print(f"      ✅ 语义组数据已完全清除")
            return True
        else:
            print(f"      ❌ 仍有 {with_semantic} 条语义组数据")
            return False
            
    except Exception as e:
        print(f"   ❌ 数据库测试失败: {e}")
        return False
    finally:
        if 'conn' in locals():
            conn.close()

def test_services():
    """测试推荐服务"""
    print("\n🔍 测试推荐服务...")
    
    try:
        from src.services.linguistic_recommender import LinguisticRecommenderService
        
        service = LinguisticRecommenderService('instance/words.db')
        
        # 测试统计信息
        stats = service.get_linguistic_statistics()
        
        print(f"   📊 推荐服务统计:")
        print(f"      - 总单词数: {stats.get('total_words', 0)}")
        print(f"      - 已标注单词: {stats.get('total_annotated', 0)}")
        
        # 检查是否还有语义组统计
        if 'top_semantic_groups' in stats:
            print(f"      ❌ 仍有语义组统计")
            return False
        else:
            print(f"      ✅ 语义组统计已移除")
            
        # 测试推荐功能
        try:
            recommendations = service.get_linguistic_recommendations(
                user_id=1, 
                word_id=1, 
                context='learning'
            )
            
            semantic_recs = [r for r in recommendations if 'semantic' in r.category]
            
            if semantic_recs:
                print(f"      ❌ 仍有语义推荐: {len(semantic_recs)} 个")
                return False
            else:
                print(f"      ✅ 语义推荐已移除")
                print(f"      📝 剩余推荐数: {len(recommendations)}")
                
        except Exception as e:
            print(f"      ⚠️  推荐测试异常: {e}")
            
        return True
        
    except Exception as e:
        print(f"   ❌ 服务测试失败: {e}")
        return False

def test_config():
    """测试配置"""
    print("\n🔍 测试配置文件...")
    
    try:
        from src.services.pattern.recommendation_config import RecommendationConfig
        
        config = RecommendationConfig()
        
        # 检查配置属性
        config_dict = config.__dict__
        
        semantic_keys = [k for k in config_dict.keys() if 'semantic' in k.lower()]
        
        print(f"   📊 配置检查:")
        print(f"      - 总配置项: {len(config_dict)}")
        print(f"      - 语义相关: {len(semantic_keys)}")
        
        if semantic_keys:
            print(f"      ❌ 仍有语义配置: {semantic_keys}")
            return False
        else:
            print(f"      ✅ 语义配置已移除")
            
        # 显示剩余配置
        print(f"      📝 剩余配置:")
        for key, value in config_dict.items():
            print(f"         - {key}: {value}")
            
        return True
        
    except Exception as e:
        print(f"   ❌ 配置测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🗑️ 语义组功能移除验证测试 (简化版)")
    print("=" * 50)
    
    tests = [
        ("数据库清理", test_database),
        ("推荐服务", test_services),
        ("配置文件", test_config)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            status = "✅ 通过" if result else "❌ 失败"
            print(f"\n{test_name}: {status}")
            if result:
                passed += 1
        except Exception as e:
            print(f"\n{test_name}: ❌ 异常 - {e}")
    
    print("\n" + "=" * 50)
    print(f"🎯 测试结果: {passed}/{total} 项通过")
    
    if passed == total:
        print("🎉 语义组功能已完全移除！")
        print("\n✅ 移除效果确认:")
        print("   - 数据库中的语义组数据已清空")
        print("   - 推荐服务中的语义推荐逻辑已移除")
        print("   - 配置文件中的语义相关设置已禁用")
        print("   - 系统运行正常，无语义组功能")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关组件")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
