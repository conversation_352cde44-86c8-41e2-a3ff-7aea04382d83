#!/usr/bin/env python3
"""
检查用户词汇数据
"""

import sqlite3

def main():
    conn = sqlite3.connect('instance/words.db')
    cursor = conn.cursor()
    
    # 检查用户词汇总数
    cursor.execute("SELECT COUNT(*) FROM user_word WHERE user_id = 1")
    total = cursor.fetchone()[0]
    print(f"用户1总词汇数: {total}")
    
    # 检查非新词汇数
    cursor.execute("SELECT COUNT(*) FROM user_word WHERE user_id = 1 AND status != 'new'")
    non_new = cursor.fetchone()[0]
    print(f"用户1非新词汇数: {non_new}")
    
    # 检查有标注的词汇数
    cursor.execute("""
        SELECT COUNT(*) 
        FROM user_word uw
        JOIN word w ON uw.word_id = w.id
        LEFT JOIN word_linguistic_annotations wla ON w.id = wla.word_id
        WHERE uw.user_id = 1 AND uw.status != 'new' AND wla.word_id IS NOT NULL
    """)
    with_annotations = cursor.fetchone()[0]
    print(f"用户1有标注的非新词汇数: {with_annotations}")
    
    # 检查具体的词汇
    cursor.execute("""
        SELECT w.english_word, uw.status, uw.proficiency,
               CASE WHEN wla.word_id IS NOT NULL THEN 'Yes' ELSE 'No' END as has_annotation
        FROM user_word uw
        JOIN word w ON uw.word_id = w.id
        LEFT JOIN word_linguistic_annotations wla ON w.id = wla.word_id
        WHERE uw.user_id = 1 AND uw.status != 'new'
        ORDER BY w.english_word
    """)
    
    words = cursor.fetchall()
    print(f"\n用户1的非新词汇列表:")
    for word, status, proficiency, has_annotation in words:
        print(f"  {word}: {status}, 熟练度={proficiency}, 有标注={has_annotation}")
    
    conn.close()

if __name__ == '__main__':
    main()
