#!/usr/bin/env python3
"""
创建语言学专家推荐反馈相关的数据库表
"""

import sqlite3
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def create_feedback_tables():
    """创建反馈相关的数据库表"""

    # 连接数据库
    db_path = 'instance/words.db'
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # 1. 创建推荐质量统计表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS recommendation_quality_stats (
                word_id INTEGER PRIMARY KEY,
                total_ratings INTEGER DEFAULT 0,
                excellent_count INTEGER DEFAULT 0,
                good_count INTEGER DEFAULT 0,
                neutral_count INTEGER DEFAULT 0,
                poor_count INTEGER DEFAULT 0,
                average_score REAL DEFAULT 0,
                last_updated DATETIME,
                FOREIGN KEY (word_id) REFERENCES word (id)
            )
        ''')

        # 2. 创建推荐改进建议表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS recommendation_improvements (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                word_id INTEGER,
                group_type TEXT,
                feedback_type TEXT,
                improvement_suggestion TEXT,
                status TEXT DEFAULT 'pending',
                created_at DATETIME,
                resolved_at DATETIME,
                FOREIGN KEY (word_id) REFERENCES word (id)
            )
        ''')

        # 3. 扩展用户交互表（如果不存在）
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS user_pattern_interactions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                interaction_id TEXT UNIQUE,
                user_id INTEGER,
                word_id INTEGER,
                interaction_type TEXT,
                interaction_data TEXT,
                created_at DATETIME,
                FOREIGN KEY (word_id) REFERENCES word (id)
            )
        ''')
        
        # 4. 创建会话跟踪表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS user_sessions (
                session_id TEXT PRIMARY KEY,
                user_id INTEGER,
                start_time DATETIME,
                end_time DATETIME,
                total_interactions INTEGER DEFAULT 0,
                words_viewed TEXT,
                recommendations_shown INTEGER DEFAULT 0,
                feedback_given INTEGER DEFAULT 0
            )
        ''')
        
        # 5. 为现有的word表添加交互统计字段（如果不存在）
        try:
            cursor.execute('ALTER TABLE word ADD COLUMN interaction_count INTEGER DEFAULT 0')
        except sqlite3.OperationalError:
            # 字段已存在
            pass

        try:
            cursor.execute('ALTER TABLE word ADD COLUMN last_interaction DATETIME')
        except sqlite3.OperationalError:
            # 字段已存在
            pass
        
        # 6. 创建索引以提高查询性能
        cursor.execute('''
            CREATE INDEX IF NOT EXISTS idx_interactions_word_id 
            ON user_pattern_interactions(word_id)
        ''')
        
        cursor.execute('''
            CREATE INDEX IF NOT EXISTS idx_interactions_type 
            ON user_pattern_interactions(interaction_type)
        ''')
        
        cursor.execute('''
            CREATE INDEX IF NOT EXISTS idx_interactions_created 
            ON user_pattern_interactions(created_at)
        ''')
        
        cursor.execute('''
            CREATE INDEX IF NOT EXISTS idx_quality_stats_word 
            ON recommendation_quality_stats(word_id)
        ''')
        
        cursor.execute('''
            CREATE INDEX IF NOT EXISTS idx_improvements_word 
            ON recommendation_improvements(word_id)
        ''')
        
        cursor.execute('''
            CREATE INDEX IF NOT EXISTS idx_improvements_status 
            ON recommendation_improvements(status)
        ''')
        
        # 提交更改
        conn.commit()
        print("✅ 反馈相关数据库表创建成功！")
        
        # 显示创建的表信息
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE '%recommendation%' OR name LIKE '%interaction%' OR name LIKE '%session%'")
        tables = cursor.fetchall()
        
        print("\n📊 创建的表：")
        for table in tables:
            print(f"  - {table[0]}")
            
        # 显示表结构
        print("\n📋 表结构详情：")
        for table in tables:
            table_name = table[0]
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()
            print(f"\n{table_name}:")
            for col in columns:
                print(f"  {col[1]} {col[2]} {'NOT NULL' if col[3] else ''} {'PRIMARY KEY' if col[5] else ''}")
        
    except Exception as e:
        print(f"❌ 创建表时出错: {str(e)}")
        conn.rollback()
        return False
    
    finally:
        conn.close()
    
    return True

def verify_tables():
    """验证表是否创建成功"""
    db_path = 'instance/words.db'
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # 检查关键表是否存在
        required_tables = [
            'recommendation_quality_stats',
            'recommendation_improvements', 
            'user_pattern_interactions',
            'user_sessions'
        ]
        
        for table in required_tables:
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table,))
            if not cursor.fetchone():
                print(f"❌ 表 {table} 不存在")
                return False
        
        print("✅ 所有必需的表都已创建")
        
        # 检查word表的新字段
        cursor.execute("PRAGMA table_info(word)")
        columns = [col[1] for col in cursor.fetchall()]

        if 'interaction_count' not in columns:
            print("❌ word表缺少interaction_count字段")
            return False

        if 'last_interaction' not in columns:
            print("❌ word表缺少last_interaction字段")
            return False

        print("✅ word表字段扩展成功")
        return True
        
    except Exception as e:
        print(f"❌ 验证表时出错: {str(e)}")
        return False
    
    finally:
        conn.close()

if __name__ == '__main__':
    print("🚀 开始创建语言学专家推荐反馈数据库表...")
    
    if create_feedback_tables():
        if verify_tables():
            print("\n🎉 数据库表创建和验证完成！")
            print("\n📝 接下来可以：")
            print("1. 运行注释增强脚本：python scripts/enhance_linguistic_annotations.py")
            print("2. 测试新的反馈API端点")
            print("3. 在主应用中注册新的Blueprint")
        else:
            print("\n❌ 表验证失败，请检查错误信息")
    else:
        print("\n❌ 表创建失败")
