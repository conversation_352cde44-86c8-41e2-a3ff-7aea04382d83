#!/usr/bin/env python3
"""
语言学标注智能增强脚本
基于现有数据和语言学规则，自动补全缺失的标注
"""

import sqlite3
import json
import re
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class WordAnalysis:
    """单词分析结果"""
    word_id: int
    english_word: str
    chinese_meaning: str
    has_true_prefix: bool
    prefix: Optional[str]
    root: Optional[str]
    suffix: Optional[str]
    linguistic_tags: List[str]
    semantic_groups: List[str]
    morphology_type: str
    confidence: float

class LinguisticAnnotationEnhancer:
    """语言学标注增强器"""
    
    def __init__(self, db_path: str):
        self.db_path = db_path
        
        # 真前缀词典 (基于语言学研究)
        self.true_prefixes = {
            'un-': {'meaning': '否定', 'examples': ['unhappy', 'unable', 'unfair']},
            're-': {'meaning': '重复/再次', 'examples': ['return', 'repeat', 'review']},
            'pre-': {'meaning': '预先', 'examples': ['prepare', 'preview', 'predict']},
            'dis-': {'meaning': '否定/分离', 'examples': ['disagree', 'disappear', 'disconnect']},
            'mis-': {'meaning': '错误', 'examples': ['mistake', 'misunderstand', 'mislead']},
            'over-': {'meaning': '过度', 'examples': ['overeat', 'overtime', 'overcome']},
            'under-': {'meaning': '不足/在下', 'examples': ['understand', 'underground', 'underline']},
            'inter-': {'meaning': '之间', 'examples': ['international', 'internet', 'interview']},
            'super-': {'meaning': '超级', 'examples': ['superman', 'supermarket', 'superior']},
            'anti-': {'meaning': '反对', 'examples': ['antiwar', 'antibody', 'antisocial']},
            'auto-': {'meaning': '自动', 'examples': ['automatic', 'automobile', 'autobiography']},
            'co-': {'meaning': '共同', 'examples': ['cooperation', 'coordinate', 'coexist']},
            'ex-': {'meaning': '向外/前任', 'examples': ['export', 'express', 'example']},
            'in-': {'meaning': '向内/否定', 'examples': ['import', 'include', 'incorrect']},
            'non-': {'meaning': '非', 'examples': ['nonsense', 'nonfiction', 'nonprofit']},
            'sub-': {'meaning': '在下', 'examples': ['subway', 'submarine', 'subtitle']},
            'trans-': {'meaning': '跨越', 'examples': ['transport', 'translate', 'transform']},
            'uni-': {'meaning': '单一', 'examples': ['university', 'uniform', 'unique']},
            'bi-': {'meaning': '双', 'examples': ['bicycle', 'bilingual', 'biography']},
            'tri-': {'meaning': '三', 'examples': ['triangle', 'tricycle', 'triple']},
            'multi-': {'meaning': '多', 'examples': ['multiple', 'multimedia', 'multicultural']},
            'semi-': {'meaning': '半', 'examples': ['semicircle', 'semifinal', 'semiconductor']},
            'micro-': {'meaning': '微小', 'examples': ['microscope', 'microphone', 'microwave']},
            'macro-': {'meaning': '宏大', 'examples': ['macroeconomics', 'macroscopic']},
            'mini-': {'meaning': '小', 'examples': ['minimum', 'miniature', 'minimize']},
            'mega-': {'meaning': '巨大', 'examples': ['megabyte', 'megaphone', 'megalopolis']},
            'ultra-': {'meaning': '极端', 'examples': ['ultraviolet', 'ultrasound', 'ultramodern']},
            'hyper-': {'meaning': '超过', 'examples': ['hyperactive', 'hyperlink', 'hypersensitive']},
            'pseudo-': {'meaning': '假的', 'examples': ['pseudonym', 'pseudoscience']},
            'proto-': {'meaning': '原始', 'examples': ['prototype', 'protocol', 'protozoa']},
            'neo-': {'meaning': '新的', 'examples': ['neonatal', 'neoclassical', 'neologism']},
            'retro-': {'meaning': '向后', 'examples': ['retrospective', 'retrograde', 'retrofit']},
            'counter-': {'meaning': '反对', 'examples': ['counteract', 'counterpart', 'counterclockwise']},
            'out-': {'meaning': '超出', 'examples': ['output', 'outcome', 'outstanding']},
            'up-': {'meaning': '向上', 'examples': ['update', 'upgrade', 'upstairs']},
            'down-': {'meaning': '向下', 'examples': ['download', 'downgrade', 'downstairs']},
            'fore-': {'meaning': '前面', 'examples': ['forecast', 'foreground', 'foresee']},
            'back-': {'meaning': '向后', 'examples': ['background', 'backward', 'backup']},
            'self-': {'meaning': '自己', 'examples': ['self-control', 'self-service', 'selfish']},
            'well-': {'meaning': '好的', 'examples': ['well-known', 'well-being', 'welcome']},
            'ill-': {'meaning': '坏的', 'examples': ['ill-health', 'illegal', 'illiterate']},
            'mal-': {'meaning': '坏的', 'examples': ['malfunction', 'malnutrition', 'malpractice']},
            'bene-': {'meaning': '好的', 'examples': ['benefit', 'beneficial', 'benevolent']},
            'eu-': {'meaning': '好的', 'examples': ['euphemism', 'euphoria', 'euthanasia']},
            'dys-': {'meaning': '坏的', 'examples': ['dysfunction', 'dyslexia', 'dystopia']},
        }
        
        # 常见后缀
        self.common_suffixes = {
            '-ing': {'type': '动名词/现在分词', 'examples': ['running', 'swimming', 'reading']},
            '-ed': {'type': '过去式/过去分词', 'examples': ['played', 'worked', 'finished']},
            '-er': {'type': '人/比较级', 'examples': ['teacher', 'bigger', 'worker']},
            '-est': {'type': '最高级', 'examples': ['biggest', 'fastest', 'strongest']},
            '-ly': {'type': '副词', 'examples': ['quickly', 'slowly', 'carefully']},
            '-tion': {'type': '名词', 'examples': ['education', 'information', 'creation']},
            '-sion': {'type': '名词', 'examples': ['decision', 'conclusion', 'discussion']},
            '-ness': {'type': '名词(状态)', 'examples': ['happiness', 'darkness', 'kindness']},
            '-ment': {'type': '名词(行为)', 'examples': ['movement', 'development', 'agreement']},
            '-ful': {'type': '形容词(充满)', 'examples': ['beautiful', 'helpful', 'wonderful']},
            '-less': {'type': '形容词(缺乏)', 'examples': ['homeless', 'hopeless', 'careless']},
            '-able': {'type': '形容词(能够)', 'examples': ['comfortable', 'available', 'reasonable']},
            '-ible': {'type': '形容词(能够)', 'examples': ['possible', 'terrible', 'incredible']},
            '-ous': {'type': '形容词', 'examples': ['famous', 'dangerous', 'serious']},
            '-ious': {'type': '形容词', 'examples': ['curious', 'previous', 'obvious']},
            '-al': {'type': '形容词', 'examples': ['natural', 'personal', 'national']},
            '-ical': {'type': '形容词', 'examples': ['musical', 'physical', 'chemical']},
            '-ive': {'type': '形容词', 'examples': ['active', 'creative', 'positive']},
            '-ative': {'type': '形容词', 'examples': ['creative', 'negative', 'relative']},
            '-y': {'type': '形容词', 'examples': ['happy', 'easy', 'dirty']},
            '-ic': {'type': '形容词', 'examples': ['magic', 'plastic', 'fantastic']},
            '-ish': {'type': '形容词(有点)', 'examples': ['childish', 'selfish', 'foolish']},
            '-ward': {'type': '副词(方向)', 'examples': ['forward', 'backward', 'toward']},
            '-wise': {'type': '副词(方式)', 'examples': ['likewise', 'otherwise', 'clockwise']},
        }
        
        # 语义分组规则
        self.semantic_rules = {
            'family': ['family', 'mother', 'father', 'brother', 'sister', 'son', 'daughter', 'parent', 'child', 'baby', 'grandfather', 'grandmother', 'uncle', 'aunt', 'cousin', 'husband', 'wife'],
            'colors': ['red', 'blue', 'green', 'yellow', 'black', 'white', 'brown', 'pink', 'purple', 'orange', 'gray', 'grey'],
            'numbers': ['one', 'two', 'three', 'four', 'five', 'six', 'seven', 'eight', 'nine', 'ten', 'eleven', 'twelve', 'thirteen', 'fourteen', 'fifteen', 'sixteen', 'seventeen', 'eighteen', 'nineteen', 'twenty', 'thirty', 'forty', 'fifty', 'sixty', 'seventy', 'eighty', 'ninety', 'hundred', 'thousand', 'million', 'first', 'second', 'third', 'fourth', 'fifth', 'sixth', 'seventh', 'eighth', 'ninth', 'tenth'],
            'time': ['time', 'day', 'week', 'month', 'year', 'hour', 'minute', 'second', 'morning', 'afternoon', 'evening', 'night', 'today', 'tomorrow', 'yesterday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday', 'january', 'february', 'march', 'april', 'may', 'june', 'july', 'august', 'september', 'october', 'november', 'december'],
            'body': ['head', 'face', 'eye', 'nose', 'mouth', 'ear', 'hair', 'neck', 'shoulder', 'arm', 'hand', 'finger', 'leg', 'foot', 'toe', 'back', 'chest', 'stomach', 'heart', 'brain'],
            'food': ['food', 'eat', 'drink', 'water', 'milk', 'bread', 'rice', 'meat', 'fish', 'chicken', 'beef', 'pork', 'egg', 'apple', 'banana', 'orange', 'grape', 'strawberry', 'tomato', 'potato', 'carrot', 'onion', 'coffee', 'tea', 'juice', 'cake', 'cookie', 'chocolate', 'ice cream', 'pizza', 'hamburger', 'sandwich'],
            'animals': ['animal', 'dog', 'cat', 'bird', 'fish', 'horse', 'cow', 'pig', 'sheep', 'chicken', 'duck', 'rabbit', 'mouse', 'elephant', 'lion', 'tiger', 'bear', 'monkey', 'snake', 'frog', 'butterfly', 'bee', 'ant', 'spider'],
            'clothes': ['clothes', 'shirt', 'dress', 'pants', 'skirt', 'coat', 'jacket', 'sweater', 'shoes', 'socks', 'hat', 'cap', 'gloves', 'scarf', 'belt', 'tie', 'underwear', 'pajamas'],
            'school': ['school', 'student', 'teacher', 'class', 'lesson', 'homework', 'test', 'exam', 'book', 'pen', 'pencil', 'paper', 'desk', 'chair', 'blackboard', 'computer', 'study', 'learn', 'read', 'write', 'math', 'english', 'science', 'history', 'art', 'music', 'sports'],
            'emotions': ['happy', 'sad', 'angry', 'excited', 'surprised', 'afraid', 'worried', 'tired', 'bored', 'interested', 'love', 'like', 'hate', 'feel', 'emotion', 'mood', 'smile', 'laugh', 'cry', 'shout'],
            'weather': ['weather', 'sunny', 'cloudy', 'rainy', 'snowy', 'windy', 'hot', 'cold', 'warm', 'cool', 'dry', 'wet', 'sun', 'cloud', 'rain', 'snow', 'wind', 'storm', 'thunder', 'lightning'],
            'transportation': ['car', 'bus', 'train', 'plane', 'ship', 'boat', 'bicycle', 'motorcycle', 'taxi', 'subway', 'truck', 'van', 'drive', 'ride', 'fly', 'sail', 'walk', 'run', 'travel', 'trip', 'journey'],
            'house': ['house', 'home', 'room', 'bedroom', 'bathroom', 'kitchen', 'living room', 'dining room', 'door', 'window', 'wall', 'floor', 'ceiling', 'roof', 'stairs', 'garden', 'yard', 'garage', 'basement', 'attic'],
            'jobs': ['job', 'work', 'teacher', 'doctor', 'nurse', 'driver', 'cook', 'waiter', 'police', 'firefighter', 'farmer', 'worker', 'manager', 'engineer', 'lawyer', 'artist', 'singer', 'actor', 'writer', 'scientist'],
            'sports': ['sport', 'football', 'basketball', 'baseball', 'tennis', 'golf', 'swimming', 'running', 'jumping', 'dancing', 'boxing', 'wrestling', 'skiing', 'skating', 'cycling', 'hiking', 'climbing', 'fishing', 'hunting', 'camping'],
            'technology': ['computer', 'internet', 'website', 'email', 'phone', 'mobile', 'camera', 'television', 'radio', 'video', 'game', 'software', 'hardware', 'program', 'app', 'digital', 'online', 'offline', 'download', 'upload'],
        }
        
    def analyze_word(self, word_id: int, english_word: str, chinese_meaning: str) -> WordAnalysis:
        """分析单词的语言学特征"""
        
        # 检测真前缀
        has_prefix, prefix, root = self._detect_true_prefix(english_word)
        
        # 检测后缀
        suffix = self._detect_suffix(english_word)
        
        # 生成语言学标签
        linguistic_tags = self._generate_linguistic_tags(english_word, chinese_meaning, has_prefix, suffix)
        
        # 生成语义组
        semantic_groups = self._generate_semantic_groups(english_word, chinese_meaning)
        
        # 确定构词类型
        morphology_type = self._determine_morphology_type(english_word, has_prefix, suffix)
        
        # 计算置信度
        confidence = self._calculate_confidence(english_word, has_prefix, suffix, semantic_groups)
        
        return WordAnalysis(
            word_id=word_id,
            english_word=english_word,
            chinese_meaning=chinese_meaning,
            has_true_prefix=has_prefix,
            prefix=prefix,
            root=root,
            suffix=suffix,
            linguistic_tags=linguistic_tags,
            semantic_groups=semantic_groups,
            morphology_type=morphology_type,
            confidence=confidence
        )
    
    def _detect_true_prefix(self, word: str) -> Tuple[bool, Optional[str], Optional[str]]:
        """检测真前缀"""
        word_lower = word.lower()
        
        # 按长度排序，优先匹配长前缀
        sorted_prefixes = sorted(self.true_prefixes.keys(), key=len, reverse=True)
        
        for prefix in sorted_prefixes:
            if word_lower.startswith(prefix):
                # 验证是否为真前缀（不是偶然匹配）
                root = word_lower[len(prefix):]
                if len(root) >= 3:  # 词根至少3个字母
                    # 检查是否在例词中或符合语言学规律
                    examples = self.true_prefixes[prefix]['examples']
                    if word_lower in [ex.lower() for ex in examples] or self._is_valid_prefix_combination(prefix, root):
                        return True, prefix, root
        
        return False, None, word_lower
    
    def _is_valid_prefix_combination(self, prefix: str, root: str) -> bool:
        """验证前缀+词根组合的有效性"""
        # 简单的语言学规律检查
        if len(root) < 3:
            return False
        
        # 检查词根是否以常见字母开头
        common_root_starts = ['a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z']
        if root[0] not in common_root_starts:
            return False
        
        # 特殊规律检查
        if prefix in ['un-', 'dis-', 'in-', 'im-', 'ir-', 'il-'] and root.startswith(('a', 'e', 'i', 'o', 'u')):
            return True
        
        return True  # 默认认为有效
    
    def _detect_suffix(self, word: str) -> Optional[str]:
        """检测后缀"""
        word_lower = word.lower()
        
        # 按长度排序，优先匹配长后缀
        sorted_suffixes = sorted(self.common_suffixes.keys(), key=len, reverse=True)
        
        for suffix in sorted_suffixes:
            if word_lower.endswith(suffix):
                # 确保词根部分足够长
                root_length = len(word_lower) - len(suffix)
                if root_length >= 3:
                    return suffix
        
        return None
    
    def _generate_linguistic_tags(self, word: str, meaning: str, has_prefix: bool, suffix: Optional[str]) -> List[str]:
        """生成语言学标签"""
        tags = []
        
        # 基于后缀的词性标签
        if suffix:
            suffix_info = self.common_suffixes.get(suffix, {})
            suffix_type = suffix_info.get('type', '')
            
            if 'noun' in suffix_type.lower() or suffix in ['-tion', '-sion', '-ness', '-ment']:
                tags.append('名词')
            elif 'adjective' in suffix_type.lower() or suffix in ['-ful', '-less', '-able', '-ible', '-ous', '-ious', '-al', '-ical', '-ive', '-ative', '-y', '-ic', '-ish']:
                tags.append('形容词')
            elif 'adverb' in suffix_type.lower() or suffix in ['-ly', '-ward', '-wise']:
                tags.append('副词')
            elif suffix in ['-ing']:
                tags.extend(['动名词', '现在分词'])
            elif suffix in ['-ed']:
                tags.extend(['过去式', '过去分词'])
            elif suffix in ['-er']:
                if '人' in meaning or '者' in meaning:
                    tags.append('人物名词')
                else:
                    tags.append('比较级')
            elif suffix in ['-est']:
                tags.append('最高级')
        
        # 基于前缀的语义标签
        if has_prefix:
            tags.append('派生词')
            tags.append('前缀词')
        
        # 基于中文含义的标签
        if '时间' in meaning or '月' in meaning or '日' in meaning:
            tags.append('时间词')
        if '颜色' in meaning or '色' in meaning:
            tags.append('颜色词')
        if '数字' in meaning or '第' in meaning:
            tags.append('数词')
        if '家庭' in meaning or '亲' in meaning:
            tags.append('家庭词')
        if '身体' in meaning or '部位' in meaning:
            tags.append('身体词')
        if '动物' in meaning:
            tags.append('动物词')
        if '食物' in meaning or '吃' in meaning or '喝' in meaning:
            tags.append('食物词')
        if '衣服' in meaning or '穿' in meaning:
            tags.append('服装词')
        if '学校' in meaning or '学习' in meaning or '教' in meaning:
            tags.append('教育词')
        if '情感' in meaning or '感情' in meaning or '心情' in meaning:
            tags.append('情感词')
        if '天气' in meaning or '气候' in meaning:
            tags.append('天气词')
        if '交通' in meaning or '车' in meaning or '船' in meaning or '飞机' in meaning:
            tags.append('交通词')
        if '房子' in meaning or '家' in meaning or '房间' in meaning:
            tags.append('居住词')
        if '工作' in meaning or '职业' in meaning:
            tags.append('职业词')
        if '运动' in meaning or '体育' in meaning:
            tags.append('运动词')
        if '技术' in meaning or '电脑' in meaning or '网络' in meaning:
            tags.append('科技词')
        
        # 基于英文单词的特征标签
        if word.lower() in ['january', 'february', 'march', 'april', 'may', 'june', 'july', 'august', 'september', 'october', 'november', 'december']:
            tags.extend(['专有名词', '月份'])
        if word.lower() in ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']:
            tags.extend(['专有名词', '星期'])
        if word[0].isupper():
            tags.append('专有名词')
        
        return list(set(tags))  # 去重
    
    def _generate_semantic_groups(self, word: str, meaning: str) -> List[str]:
        """生成语义组 - 已禁用"""
        return []  # 语义组功能已彻底移除
        
        # 基于中文含义匹配
        if '家庭' in meaning or '亲' in meaning or any(family_word in meaning for family_word in ['父', '母', '兄', '弟', '姐', '妹', '儿', '女', '爷', '奶', '叔', '姨', '夫', '妻']):
            groups.append('family')
        if '颜色' in meaning or '色' in meaning:
            groups.append('colors')
        if '数字' in meaning or '第' in meaning or any(num in meaning for num in ['一', '二', '三', '四', '五', '六', '七', '八', '九', '十']):
            groups.append('numbers')
        if '时间' in meaning or '月' in meaning or '日' in meaning or '年' in meaning or '小时' in meaning or '分钟' in meaning or '秒' in meaning:
            groups.append('time')
        if '身体' in meaning or '部位' in meaning or any(body_part in meaning for body_part in ['头', '脸', '眼', '鼻', '嘴', '耳', '手', '脚', '腿', '胳膊']):
            groups.append('body')
        if '食物' in meaning or '吃' in meaning or '喝' in meaning or any(food in meaning for food in ['肉', '鱼', '蛋', '奶', '面包', '米饭', '水果', '蔬菜']):
            groups.append('food')
        if '动物' in meaning or any(animal in meaning for animal in ['狗', '猫', '鸟', '鱼', '马', '牛', '猪', '羊', '鸡', '鸭']):
            groups.append('animals')
        if '衣服' in meaning or '穿' in meaning or any(clothes in meaning for clothes in ['衬衫', '裙子', '裤子', '外套', '鞋子', '帽子']):
            groups.append('clothes')
        if '学校' in meaning or '学习' in meaning or '教' in meaning or any(school in meaning for school in ['学生', '老师', '课', '作业', '考试', '书']):
            groups.append('school')
        if '情感' in meaning or '感情' in meaning or '心情' in meaning or any(emotion in meaning for emotion in ['高兴', '伤心', '生气', '兴奋', '害怕', '担心']):
            groups.append('emotions')
        if '天气' in meaning or '气候' in meaning or any(weather in meaning for weather in ['晴', '阴', '雨', '雪', '风', '热', '冷']):
            groups.append('weather')
        if '交通' in meaning or '车' in meaning or '船' in meaning or '飞机' in meaning or any(transport in meaning for transport in ['汽车', '公交', '火车', '地铁', '自行车']):
            groups.append('transportation')
        if '房子' in meaning or '家' in meaning or '房间' in meaning or any(house in meaning for house in ['卧室', '厨房', '客厅', '浴室', '门', '窗']):
            groups.append('house')
        if '工作' in meaning or '职业' in meaning or any(job in meaning for job in ['医生', '护士', '司机', '厨师', '警察', '农民']):
            groups.append('jobs')
        if '运动' in meaning or '体育' in meaning or any(sport in meaning for sport in ['足球', '篮球', '网球', '游泳', '跑步', '跳舞']):
            groups.append('sports')
        if '技术' in meaning or '电脑' in meaning or '网络' in meaning or any(tech in meaning for tech in ['计算机', '互联网', '手机', '软件', '程序']):
            groups.append('technology')
        
        return list(set(groups))  # 去重
    
    def _determine_morphology_type(self, word: str, has_prefix: bool, suffix: Optional[str]) -> str:
        """确定构词类型"""
        if has_prefix and suffix:
            return 'complex_derivative'  # 复合派生词
        elif has_prefix:
            return 'prefix_derivative'  # 前缀派生词
        elif suffix:
            return 'suffix_derivative'  # 后缀派生词
        elif ' ' in word or '-' in word:
            return 'compound'  # 复合词
        else:
            return 'simple'  # 简单词
    
    def _calculate_confidence(self, word: str, has_prefix: bool, suffix: Optional[str], semantic_groups: List[str]) -> float:
        """计算标注置信度"""
        confidence = 0.5  # 基础置信度
        
        # 前缀检测增加置信度
        if has_prefix:
            confidence += 0.2
        
        # 后缀检测增加置信度
        if suffix:
            confidence += 0.1
        
        # 语义组数量增加置信度
        confidence += min(len(semantic_groups) * 0.05, 0.2)
        
        # 单词长度影响置信度
        if len(word) >= 6:
            confidence += 0.1
        
        return min(confidence, 1.0)
    
    def enhance_all_annotations(self) -> Dict[str, int]:
        """增强所有单词的语言学标注"""
        stats = {
            'total_words': 0,
            'enhanced_words': 0,
            'new_prefixes': 0,
            'new_semantic_groups': 0,
            'errors': 0
        }
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 获取所有单词
            cursor.execute("""
                SELECT w.id, w.english_word, w.chinese_meaning,
                       wla.has_true_prefix, wla.prefix, wla.linguistic_tags, wla.semantic_groups
                FROM word w
                LEFT JOIN word_linguistic_annotations wla ON w.id = wla.word_id
            """)
            
            words = cursor.fetchall()
            stats['total_words'] = len(words)
            
            for word_data in words:
                try:
                    word_id, english_word, chinese_meaning, existing_prefix, existing_prefix_text, existing_tags, existing_groups = word_data
                    
                    # 分析单词
                    analysis = self.analyze_word(word_id, english_word, chinese_meaning)
                    
                    # 准备更新数据
                    linguistic_tags_json = json.dumps(analysis.linguistic_tags, ensure_ascii=False)
                    semantic_groups_json = json.dumps(analysis.semantic_groups, ensure_ascii=False)
                    
                    if existing_prefix is None:
                        # 插入新标注
                        cursor.execute("""
                            INSERT OR REPLACE INTO word_linguistic_annotations 
                            (word_id, has_true_prefix, prefix, root, suffix, linguistic_tags, semantic_groups, 
                             morphology_type, annotation_confidence, annotated_by, created_at, updated_at)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'auto_enhancer', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                        """, (
                            word_id, 
                            1 if analysis.has_true_prefix else 0,
                            analysis.prefix,
                            analysis.root,
                            analysis.suffix,
                            linguistic_tags_json,
                            semantic_groups_json,
                            analysis.morphology_type,
                            analysis.confidence
                        ))
                        stats['enhanced_words'] += 1
                    else:
                        # 更新现有标注
                        updates_made = False
                        
                        # 检查是否需要更新前缀信息
                        if not existing_prefix and analysis.has_true_prefix:
                            cursor.execute("""
                                UPDATE word_linguistic_annotations 
                                SET has_true_prefix = ?, prefix = ?, root = ?, updated_at = CURRENT_TIMESTAMP
                                WHERE word_id = ?
                            """, (1, analysis.prefix, analysis.root, word_id))
                            stats['new_prefixes'] += 1
                            updates_made = True
                        
                        # 检查是否需要更新语义组
                        if existing_groups:
                            try:
                                existing_groups_list = json.loads(existing_groups)
                            except:
                                existing_groups_list = []
                        else:
                            existing_groups_list = []
                        
                        # 合并语义组
                        merged_groups = list(set(existing_groups_list + analysis.semantic_groups))
                        if len(merged_groups) > len(existing_groups_list):
                            cursor.execute("""
                                UPDATE word_linguistic_annotations 
                                SET semantic_groups = ?, updated_at = CURRENT_TIMESTAMP
                                WHERE word_id = ?
                            """, (json.dumps(merged_groups, ensure_ascii=False), word_id))
                            stats['new_semantic_groups'] += 1
                            updates_made = True
                        
                        # 更新语言学标签
                        if existing_tags:
                            try:
                                existing_tags_list = json.loads(existing_tags)
                            except:
                                existing_tags_list = []
                        else:
                            existing_tags_list = []
                        
                        merged_tags = list(set(existing_tags_list + analysis.linguistic_tags))
                        if len(merged_tags) > len(existing_tags_list):
                            cursor.execute("""
                                UPDATE word_linguistic_annotations 
                                SET linguistic_tags = ?, updated_at = CURRENT_TIMESTAMP
                                WHERE word_id = ?
                            """, (json.dumps(merged_tags, ensure_ascii=False), word_id))
                            updates_made = True
                        
                        if updates_made:
                            stats['enhanced_words'] += 1
                    
                except Exception as e:
                    logger.error(f"处理单词 {english_word} 时出错: {e}")
                    stats['errors'] += 1
            
            conn.commit()
            conn.close()
            
            logger.info(f"标注增强完成: {stats}")
            return stats
            
        except Exception as e:
            logger.error(f"标注增强失败: {e}")
            stats['errors'] += 1
            return stats

def main():
    """主函数"""
    import sys
    import os
    
    # 获取数据库路径
    if len(sys.argv) > 1:
        db_path = sys.argv[1]
    else:
        # 默认路径
        script_dir = os.path.dirname(os.path.abspath(__file__))
        db_path = os.path.join(script_dir, '..', 'instance', 'words.db')
    
    if not os.path.exists(db_path):
        logger.error(f"数据库文件不存在: {db_path}")
        return
    
    logger.info(f"开始增强语言学标注，数据库: {db_path}")
    
    enhancer = LinguisticAnnotationEnhancer(db_path)
    stats = enhancer.enhance_all_annotations()
    
    print("\n🎉 语言学标注增强完成!")
    print(f"📊 统计信息:")
    print(f"  - 总单词数: {stats['total_words']}")
    print(f"  - 增强单词数: {stats['enhanced_words']}")
    print(f"  - 新增前缀标注: {stats['new_prefixes']}")
    print(f"  - 新增语义组: {stats['new_semantic_groups']}")
    print(f"  - 错误数: {stats['errors']}")
    
    if stats['enhanced_words'] > 0:
        print(f"\n✅ 成功增强了 {stats['enhanced_words']} 个单词的语言学标注")
        print("🔍 建议接下来:")
        print("  1. 运行语言学推荐系统测试")
        print("  2. 检查新增标注的质量")
        print("  3. 收集用户反馈进行进一步优化")

if __name__ == "__main__":
    main()
