#!/usr/bin/env python3
"""
使用 Hugging Face Diffusers 生成单词 "flower" 的图片
"""

import os
import sys
import torch
from pathlib import Path
from PIL import Image, ImageDraw, ImageFont
import random
import math

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def create_simple_flower_image():
    """创建简单的花朵图片（当无法使用AI模型时的备选方案）"""
    print("🎨 创建简单的花朵图片...")

    # 创建输出目录
    output_dir = project_root / "static" / "images" / "generated"
    output_dir.mkdir(parents=True, exist_ok=True)

    # 创建学习用图片目录
    learning_dir = project_root / "static" / "images" / "words"
    learning_dir.mkdir(parents=True, exist_ok=True)

    # 创建几种不同风格的花朵图片
    flower_styles = [
        {"name": "简单花朵", "colors": [(255, 100, 150), (255, 200, 100), (100, 255, 100)]},
        {"name": "彩色花朵", "colors": [(255, 50, 100), (100, 150, 255), (255, 255, 100)]},
        {"name": "优雅花朵", "colors": [(200, 100, 200), (150, 200, 255), (255, 150, 150)]},
        {"name": "自然花朵", "colors": [(255, 180, 200), (100, 200, 100), (255, 220, 100)]}
    ]

    for i, style in enumerate(flower_styles, 1):
        # 创建画布
        width, height = 512, 512
        image = Image.new('RGB', (width, height), (240, 248, 255))  # 淡蓝色背景
        draw = ImageDraw.Draw(image)

        # 绘制花朵
        center_x, center_y = width // 2, height // 2

        # 绘制花茎
        stem_color = (34, 139, 34)  # 森林绿
        draw.rectangle([center_x - 5, center_y + 50, center_x + 5, height - 50], fill=stem_color)

        # 绘制叶子
        leaf_points = [
            (center_x - 30, center_y + 80),
            (center_x - 50, center_y + 100),
            (center_x - 20, center_y + 120),
            (center_x - 10, center_y + 100)
        ]
        draw.polygon(leaf_points, fill=(50, 150, 50))

        # 绘制花瓣
        petal_color = style["colors"][0]
        petal_radius = 60
        num_petals = 8

        for j in range(num_petals):
            angle = (2 * math.pi * j) / num_petals
            petal_x = center_x + int(petal_radius * 0.7 * math.cos(angle))
            petal_y = center_y + int(petal_radius * 0.7 * math.sin(angle))

            # 绘制椭圆形花瓣
            draw.ellipse([
                petal_x - 25, petal_y - 40,
                petal_x + 25, petal_y + 40
            ], fill=petal_color, outline=(200, 50, 100))

        # 绘制花心
        center_color = style["colors"][1]
        draw.ellipse([
            center_x - 20, center_y - 20,
            center_x + 20, center_y + 20
        ], fill=center_color, outline=(150, 100, 50))

        # 添加一些装饰点
        for _ in range(10):
            dot_x = center_x + random.randint(-15, 15)
            dot_y = center_y + random.randint(-15, 15)
            draw.ellipse([dot_x - 2, dot_y - 2, dot_x + 2, dot_y + 2],
                        fill=style["colors"][2])

        # 保存图片
        filename = f"flower_{i}.jpg"
        filepath = output_dir / filename
        image.save(filepath, quality=95)
        print(f"   ✅ 保存: {filepath}")

    # 创建专用于学习的花朵图片
    print("🌺 创建专用于单词学习的花朵图片...")

    # 创建更清晰、更适合学习的花朵图片
    width, height = 512, 512
    image = Image.new('RGB', (width, height), (255, 255, 255))  # 白色背景
    draw = ImageDraw.Draw(image)

    center_x, center_y = width // 2, height // 2 - 50

    # 绘制花茎
    draw.rectangle([center_x - 8, center_y + 80, center_x + 8, height - 30],
                  fill=(34, 139, 34))

    # 绘制叶子
    leaf_points = [
        (center_x - 40, center_y + 100),
        (center_x - 70, center_y + 130),
        (center_x - 30, center_y + 160),
        (center_x - 15, center_y + 130)
    ]
    draw.polygon(leaf_points, fill=(50, 150, 50), outline=(30, 100, 30))

    # 绘制花瓣 - 使用经典的红色
    petal_color = (220, 20, 60)  # 深红色
    petal_radius = 80
    num_petals = 6

    for j in range(num_petals):
        angle = (2 * math.pi * j) / num_petals
        petal_x = center_x + int(petal_radius * 0.6 * math.cos(angle))
        petal_y = center_y + int(petal_radius * 0.6 * math.sin(angle))

        # 绘制心形花瓣
        draw.ellipse([
            petal_x - 35, petal_y - 50,
            petal_x + 35, petal_y + 50
        ], fill=petal_color, outline=(150, 0, 30), width=2)

    # 绘制花心
    draw.ellipse([
        center_x - 25, center_y - 25,
        center_x + 25, center_y + 25
    ], fill=(255, 215, 0), outline=(200, 165, 0), width=2)  # 金色花心

    # 添加花心细节
    for _ in range(8):
        dot_x = center_x + random.randint(-15, 15)
        dot_y = center_y + random.randint(-15, 15)
        draw.ellipse([dot_x - 1, dot_y - 1, dot_x + 1, dot_y + 1],
                    fill=(200, 100, 0))

    # 保存学习用图片
    learning_filepath = learning_dir / "flower.jpg"
    image.save(learning_filepath, quality=95)
    print(f"   ✅ 学习用图片保存: {learning_filepath}")

    print("\n🎉 简单花朵图片生成完成！")
    print(f"📁 生成的图片位置:")
    print(f"   - 通用图片: {output_dir}")
    print(f"   - 学习用图片: {learning_filepath}")

    return True

def check_gpu_availability():
    """检查GPU可用性"""
    if torch.cuda.is_available():
        print(f"✅ GPU 可用: {torch.cuda.get_device_name(0)}")
        print(f"   GPU 内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
        return "cuda"
    else:
        print("⚠️  GPU 不可用，将使用 CPU（速度较慢）")
        return "cpu"

def generate_flower_image():
    """生成花朵图片"""
    print("🌸 开始生成单词 'flower' 的图片...")

    # 检查设备
    device = check_gpu_availability()

    try:
        # 尝试使用更小的模型或者创建简单的花朵图片
        print("📥 尝试加载轻量级模型或创建简单图片...")

        # 如果网络有问题，我们创建一个简单的花朵图片
        return create_simple_flower_image()
        
    except Exception as e:
        print(f"❌ 生成图片时出错: {str(e)}")
        print("💡 可能的解决方案:")
        print("   1. 确保网络连接正常（需要下载模型）")
        print("   2. 确保有足够的磁盘空间（模型约4GB）")
        print("   3. 如果使用GPU，确保CUDA和PyTorch正确安装")
        print("   4. 尝试使用CPU模式（设置 device='cpu'）")
        return False

def main():
    """主函数"""
    print("🌸 Hugging Face Diffusers 图片生成器")
    print("=" * 50)
    
    # 检查依赖
    try:
        import diffusers
        print(f"✅ Diffusers 版本: {diffusers.__version__}")
    except ImportError:
        print("❌ 未安装 diffusers 库")
        print("请运行: pip install diffusers transformers accelerate")
        return False
    
    # 生成图片
    success = generate_flower_image()
    
    if success:
        print("\n🎯 使用建议:")
        print("1. 查看生成的图片质量")
        print("2. 如需调整，可修改提示词")
        print("3. 可以尝试不同的随机种子获得不同效果")
        print("4. 生成的学习用图片可直接用于单词学习应用")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
