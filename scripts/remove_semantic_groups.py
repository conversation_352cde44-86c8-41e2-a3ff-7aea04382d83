#!/usr/bin/env python3
"""
彻底移除语义组分类功能
清理数据库中的所有语义组数据
"""

import sqlite3
import json
import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def remove_semantic_groups_from_db():
    """从数据库中移除所有语义组数据"""
    
    db_path = 'instance/words.db'
    
    print("🗑️ 开始清理语义组数据...")
    print(f"数据库路径: {db_path}")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. 检查当前语义组数据统计
        print("\n📊 清理前统计:")
        cursor.execute("""
            SELECT COUNT(*) as total_words
            FROM word_linguistic_annotations
        """)
        total_annotated = cursor.fetchone()[0]
        print(f"   - 总标注单词数: {total_annotated}")
        
        cursor.execute("""
            SELECT COUNT(*) as semantic_words
            FROM word_linguistic_annotations
            WHERE semantic_groups IS NOT NULL 
            AND semantic_groups != '[]' 
            AND semantic_groups != ''
        """)
        semantic_words = cursor.fetchone()[0]
        print(f"   - 有语义组的单词: {semantic_words}")
        
        # 2. 清空所有语义组数据
        print("\n🧹 清理语义组数据...")
        cursor.execute("""
            UPDATE word_linguistic_annotations 
            SET semantic_groups = '[]',
                updated_at = CURRENT_TIMESTAMP
            WHERE semantic_groups IS NOT NULL 
            AND semantic_groups != '[]'
        """)
        
        updated_rows = cursor.rowcount
        print(f"   ✅ 已清理 {updated_rows} 个单词的语义组数据")
        
        # 3. 验证清理结果
        print("\n🔍 验证清理结果:")
        cursor.execute("""
            SELECT COUNT(*) as remaining_semantic
            FROM word_linguistic_annotations
            WHERE semantic_groups IS NOT NULL 
            AND semantic_groups != '[]' 
            AND semantic_groups != ''
        """)
        remaining = cursor.fetchone()[0]
        print(f"   - 剩余语义组数据: {remaining}")
        
        if remaining == 0:
            print("   ✅ 语义组数据清理完成")
        else:
            print(f"   ⚠️  仍有 {remaining} 条语义组数据未清理")
        
        # 4. 提交更改
        conn.commit()
        conn.close()
        
        print(f"\n🎉 语义组数据清理完成！")
        print(f"   - 清理时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"   - 影响行数: {updated_rows}")
        
        return True
        
    except Exception as e:
        print(f"❌ 清理语义组数据失败: {str(e)}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        return False

def verify_cleanup():
    """验证清理效果"""
    
    db_path = 'instance/words.db'
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("\n🔍 最终验证:")
        
        # 检查是否还有语义组数据
        cursor.execute("""
            SELECT english_word, semantic_groups
            FROM word w
            JOIN word_linguistic_annotations wla ON w.id = wla.word_id
            WHERE wla.semantic_groups IS NOT NULL 
            AND wla.semantic_groups != '[]'
            AND wla.semantic_groups != ''
            LIMIT 5
        """)
        
        remaining_data = cursor.fetchall()
        
        if remaining_data:
            print("   ⚠️  发现残留的语义组数据:")
            for word, groups in remaining_data:
                print(f"      - {word}: {groups}")
        else:
            print("   ✅ 确认：所有语义组数据已清理完毕")
        
        # 统计清理后的数据
        cursor.execute("""
            SELECT COUNT(*) as total_clean
            FROM word_linguistic_annotations
            WHERE semantic_groups = '[]' OR semantic_groups IS NULL
        """)
        clean_count = cursor.fetchone()[0]
        
        cursor.execute("""
            SELECT COUNT(*) as total_annotations
            FROM word_linguistic_annotations
        """)
        total_count = cursor.fetchone()[0]
        
        print(f"   📊 清理统计:")
        print(f"      - 已清理单词: {clean_count}")
        print(f"      - 总标注单词: {total_count}")
        print(f"      - 清理比例: {clean_count/total_count*100:.1f}%")
        
        conn.close()
        return len(remaining_data) == 0
        
    except Exception as e:
        print(f"❌ 验证失败: {str(e)}")
        return False

if __name__ == '__main__':
    print("🗑️ 语义组分类功能移除工具")
    print(f"执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 确认操作
    print("\n⚠️  警告：此操作将永久删除所有语义组数据！")
    confirm = input("确认继续？(输入 'YES' 确认): ")
    
    if confirm != 'YES':
        print("❌ 操作已取消")
        sys.exit(0)
    
    # 执行清理
    success = remove_semantic_groups_from_db()
    
    if success:
        # 验证清理效果
        verify_success = verify_cleanup()
        
        if verify_success:
            print("\n🎉 语义组功能移除完成！")
            print("\n📝 接下来需要:")
            print("   1. 更新推荐服务代码")
            print("   2. 修改前端显示逻辑")
            print("   3. 更新配置文件")
            print("   4. 重启应用测试")
        else:
            print("\n⚠️  清理可能不完整，请检查验证结果")
            sys.exit(1)
    else:
        print("\n❌ 语义组数据清理失败")
        sys.exit(1)
