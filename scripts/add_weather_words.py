#!/usr/bin/env python3
"""
添加天气相关词汇到用户词汇库
用于测试rainy的推荐功能
"""

import sys
import os
import sqlite3

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def add_weather_words():
    """添加天气相关词汇"""
    print("🌤️ 添加天气相关词汇...")
    
    # 需要添加的词汇
    weather_words = [
        'sunny', 'cloudy', 'windy', 'snowy', 'foggy', 'stormy',
        'rain', 'snow', 'wind', 'cloud', 'sun', 'storm'
    ]
    
    try:
        conn = sqlite3.connect('instance/words.db')
        cursor = conn.cursor()
        
        added_count = 0
        
        for word in weather_words:
            # 检查词汇是否存在
            cursor.execute('SELECT id FROM word WHERE english_word = ?', (word,))
            result = cursor.fetchone()
            
            if result:
                word_id = result[0]
                
                # 检查是否已存在用户词汇记录
                cursor.execute('SELECT id FROM user_word WHERE user_id = 1 AND word_id = ?', (word_id,))
                existing = cursor.fetchone()
                
                if not existing:
                    cursor.execute("""
                        INSERT INTO user_word (user_id, word_id, status, proficiency, last_learning_date)
                        VALUES (1, ?, 'learning', 0.7, CURRENT_TIMESTAMP)
                    """, (word_id,))
                    print(f"   ✅ 添加用户词汇: {word}")
                    added_count += 1
                else:
                    print(f"   ℹ️  词汇已存在: {word}")
            else:
                print(f"   ⚠️  词汇不存在于词典: {word}")
        
        conn.commit()
        conn.close()
        
        print(f"   📊 添加完成，新增 {added_count} 个词汇")
        return True
        
    except Exception as e:
        print(f"   ❌ 添加失败: {e}")
        return False

def test_rainy_after_adding():
    """添加词汇后测试rainy推荐"""
    print(f"\n🌧️ 测试rainy推荐...")
    
    try:
        from src.services.linguistic_recommender import LinguisticRecommenderService
        
        service = LinguisticRecommenderService('instance/words.db')
        
        # 获取rainy的词汇ID
        conn = sqlite3.connect('instance/words.db')
        cursor = conn.cursor()
        cursor.execute('SELECT id FROM word WHERE english_word = ?', ('rainy',))
        result = cursor.fetchone()
        conn.close()
        
        if not result:
            print("❌ 找不到rainy词汇")
            return False
            
        word_id = result[0]
        
        # 测试推荐功能
        recommendations = service.get_linguistic_recommendations(
            user_id=1, 
            word_id=word_id, 
            context='learning'
        )
        
        print(f"📊 推荐结果:")
        print(f"   - 推荐数量: {len(recommendations)}")
        
        if recommendations:
            for i, rec in enumerate(recommendations, 1):
                print(f"\n   {i}. {rec.category_name} ({rec.category})")
                print(f"      📝 解释: {rec.explanation}")
                print(f"      🔗 相似词汇: {len(rec.similar_words)} 个")
                
                for j, word in enumerate(rec.similar_words[:3], 1):  # 只显示前3个
                    print(f"         {j}. {word.english_word} - {word.chinese_meaning}")
                    print(f"            相似原因: {word.similarity_reason}")
        else:
            print("   ℹ️  仍然无推荐")
            
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🧪 测试rainy推荐功能 - 添加相关词汇")
    print("=" * 50)
    
    # 添加天气词汇
    if add_weather_words():
        # 测试推荐
        test_rainy_after_adding()
        
        print(f"\n🎉 测试完成！")
        print(f"✅ 学习集群移除后，推荐系统在有足够相关词汇时能正常工作")
    else:
        print(f"\n❌ 添加词汇失败")
    
    return True

if __name__ == '__main__':
    main()
